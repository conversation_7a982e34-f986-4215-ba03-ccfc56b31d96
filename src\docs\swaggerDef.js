'use strict';

module.exports = {
  openapi: '3.0.0',
  info: {
    title: 'Enterprise Financial Management API',
    version: '1.0.0',
    description: 'API documentation for the Enterprise Financial Management platform with Startup Finance features',
    contact: {
      name: 'API Support',
      email: '<EMAIL>'
    }
  },
  servers: [
    {
      url: 'http://localhost:3000',
      description: 'Development server'
    }
  ],
  components: {
    securitySchemes: {
      bearerAuth: {
        type: 'http',
        scheme: 'bearer',
        bearerFormat: 'JWT'
      }
    }
  },
  security: [
    {
      bearerAuth: []
    }
  ],
  tags: [
    {
      name: 'Authentication',
      description: 'API endpoints for user authentication'
    },
    {
      name: 'Expenses',
      description: 'API endpoints for managing expenses'
    },
    {
      name: 'Approvals',
      description: 'API endpoints for expense approvals'
    },
    {
      name: 'Reports',
      description: 'API endpoints for financial reports'
    },
    {
      name: 'Banking',
      description: 'API endpoints for bank integrations'
    },
    {
      name: 'Budgets',
      description: 'API endpoints for budget management'
    },
    {
      name: 'Startup Finance',
      description: 'API endpoints for startup financial management'
    },
    {
      name: 'Scenario Planning',
      description: 'API endpoints for financial scenario planning'
    }
  ]
};