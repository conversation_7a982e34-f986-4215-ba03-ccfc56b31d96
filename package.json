{"name": "finance-manager-backend", "version": "1.0.0", "description": "Advanced Finance Manager Backend with Premium Features", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "jest --<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "db:migrate": "node scripts/migrate.js", "db:seed": "node scripts/seed.js"}, "dependencies": {"express": "^4.18.2", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "cors": "^2.8.5", "helmet": "^7.1.0", "dotenv": "^16.3.1", "pg": "^8.11.3", "redis": "^4.6.11", "axios": "^1.6.2", "exceljs": "^4.4.0", "nodemailer": "^6.9.7", "multer": "^1.4.5-lts.1", "sharp": "^0.32.6", "winston": "^3.11.0", "joi": "^17.11.0", "compression": "^1.7.4", "morgan": "^1.10.0", "cron": "^3.1.6", "aws-sdk": "^2.1498.0", "papaparse": "^5.4.1", "pdf-parse": "^1.1.1", "cohere-ai": "^7.14.0", "bull": "^4.12.2", "uuid": "^9.0.1", "mime-types": "^2.1.35", "file-type": "^18.7.0"}, "devDependencies": {"nodemon": "^3.0.2", "jest": "^29.7.0", "supertest": "^6.3.3"}, "engines": {"node": ">=18.0.0"}}