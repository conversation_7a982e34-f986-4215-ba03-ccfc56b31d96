'use strict';

module.exports = (sequelize, DataTypes) => {
  const Department = sequelize.define('Department', {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true
    },
    name: {
      type: DataTypes.STRING,
      allowNull: false
    },
    code: {
      type: DataTypes.STRING
    },
    description: {
      type: DataTypes.TEXT
    },
    companyId: {
      type: DataTypes.UUID,
      allowNull: false,
      references: {
        model: 'Companies',
        key: 'id'
      }
    },
    managerId: {
      type: DataTypes.UUID,
      references: {
        model: 'Users',
        key: 'id'
      }
    },
    parentDepartmentId: {
      type: DataTypes.UUID,
      references: {
        model: 'Departments',
        key: 'id'
      }
    },
    costCenter: {
      type: DataTypes.STRING
    },
    budgetLimit: {
      type: DataTypes.DECIMAL(19, 4)
    },
    isActive: {
      type: DataTypes.BOOLEAN,
      defaultValue: true
    }
  }, {
    timestamps: true,
    paranoid: true // Soft deletes
  });

  Department.associate = (models) => {
    Department.belongsTo(models.Company, {
      foreignKey: 'companyId',
      as: 'company'
    });
    
    Department.belongsTo(models.User, {
      foreignKey: 'managerId',
      as: 'manager'
    });
    
    Department.belongsTo(models.Department, {
      foreignKey: 'parentDepartmentId',
      as: 'parentDepartment'
    });
    
    Department.hasMany(models.Department, {
      foreignKey: 'parentDepartmentId',
      as: 'childDepartments'
    });
    
    Department.hasMany(models.User, {
      foreignKey: 'departmentId',
      as: 'users'
    });
    
    Department.hasMany(models.Budget, {
      foreignKey: 'departmentId',
      as: 'budgets'
    });

    Department.hasMany(models.Expense, {
      foreignKey: 'departmentId',
      as: 'expenses'
    });
  };

  return Department;
};