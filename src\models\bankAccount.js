'use strict';

module.exports = (sequelize, DataTypes) => {
  const BankAccount = sequelize.define('BankAccount', {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true
    },
    companyId: {
      type: DataTypes.UUID,
      allowNull: false,
      references: {
        model: 'Companies',
        key: 'id'
      }
    },
    name: {
      type: DataTypes.STRING,
      allowNull: false
    },
    accountNumber: {
      type: DataTypes.STRING,
      allowNull: false
    },
    accountType: {
      type: DataTypes.ENUM('checking', 'savings', 'credit_card', 'investment'),
      defaultValue: 'checking'
    },
    bankName: {
      type: DataTypes.STRING
    },
    routingNumber: {
      type: DataTypes.STRING
    },
    currency: {
      type: DataTypes.STRING,
      defaultValue: 'USD'
    },
    balance: {
      type: DataTypes.DECIMAL(19, 4),
      defaultValue: 0
    },
    lastSyncDate: {
      type: DataTypes.DATE
    },
    isActive: {
      type: DataTypes.BOOLEAN,
      defaultValue: true
    },
    plaidAccessToken: {
      type: DataTypes.STRING
    },
    plaidItemId: {
      type: DataTypes.STRING
    },
    plaidAccountId: {
      type: DataTypes.STRING
    },
    isDefault: {
      type: DataTypes.BOOLEAN,
      defaultValue: false
    },
    creditLimit: {
      type: DataTypes.DECIMAL(19, 4)
    }
  }, {
    timestamps: true,
    paranoid: true, // Soft deletes
    indexes: [
      { fields: ['companyId'] }
    ]
  });

  BankAccount.associate = (models) => {
    BankAccount.belongsTo(models.Company, {
      foreignKey: 'companyId',
      as: 'company'
    });
    
    BankAccount.hasMany(models.Transaction, {
      foreignKey: 'bankAccountId',
      as: 'transactions'
    });
  };

  return BankAccount;
};