'use strict';

const { Op } = require('sequelize');
const { 
  Expense, 
  User, 
  Category, 
  Department, 
  Project, 
  Receipt, 
  Approval,
  FileUpload
} = require('../../../models');
const { ApiError } = require('../../../utils/apiError');
const auditService = require('../../../services/auditService');
const notificationService = require('../../../services/notificationService');
const aiService = require('../../../services/aiService');
const currencyService = require('../../../services/currencyService');
const logger = require('../../../utils/logger');

/**
 * Get all expenses with filtering and pagination
 */
const getAllExpenses = async (req, res, next) => {
  try {
    const {
      page = 1,
      limit = 20,
      status,
      startDate,
      endDate,
      minAmount,
      maxAmount,
      categoryId,
      departmentId,
      userId,
      search,
      sortBy = 'createdAt',
      order = 'DESC'
    } = req.query;

    const where = {};
    
    // Apply company filter
    if (req.user.companyId) {
      where.companyId = req.user.companyId;
    }
    
    // Apply user filter for non-admin users
    if (req.user.role === 'employee') {
      where.userId = req.user.id;
    } else if (userId) {
      where.userId = userId;
    }
    
    // Apply filters
    if (status) where.status = status;
    if (categoryId) where.categoryId = categoryId;
    if (departmentId) where.departmentId = departmentId;
    
    if (startDate || endDate) {
      where.expenseDate = {};
      if (startDate) where.expenseDate[Op.gte] = new Date(startDate);
      if (endDate) where.expenseDate[Op.lte] = new Date(endDate);
    }
    
    if (minAmount || maxAmount) {
      where.amountInBaseCurrency = {};
      if (minAmount) where.amountInBaseCurrency[Op.gte] = parseFloat(minAmount);
      if (maxAmount) where.amountInBaseCurrency[Op.lte] = parseFloat(maxAmount);
    }
    
    if (search) {
      where[Op.or] = [
        { description: { [Op.iLike]: `%${search}%` } },
        { merchant: { [Op.iLike]: `%${search}%` } }
      ];
    }

    const offset = (page - 1) * limit;

    const { count, rows } = await Expense.findAndCountAll({
      where,
      include: [
        {
          model: User,
          as: 'user',
          attributes: ['firstName', 'lastName', 'email']
        },
        {
          model: Category,
          as: 'category',
          attributes: ['name', 'code']
        },
        {
          model: Department,
          as: 'department',
          attributes: ['name']
        },
        {
          model: Receipt,
          as: 'receipts',
          attributes: ['id', 'fileUrl', 'fileName']
        }
      ],
      order: [[sortBy, order.toUpperCase()]],
      limit: parseInt(limit),
      offset
    });

    res.status(200).json({
      status: 'success',
      data: {
        expenses: rows,
        pagination: {
          total: count,
          page: parseInt(page),
          limit: parseInt(limit),
          totalPages: Math.ceil(count / limit)
        }
      }
    });
  } catch (error) {
    logger.error('Error getting expenses:', error);
    next(error);
  }
};

/**
 * Get expense by ID
 */
const getExpenseById = async (req, res, next) => {
  try {
    const { id } = req.params;
    
    const where = { id };
    
    // Apply company filter
    if (req.user.companyId) {
      where.companyId = req.user.companyId;
    }
    
    // Apply user filter for non-admin users
    if (req.user.role === 'employee') {
      where.userId = req.user.id;
    }

    const expense = await Expense.findOne({
      where,
      include: [
        {
          model: User,
          as: 'user',
          attributes: ['firstName', 'lastName', 'email']
        },
        {
          model: Category,
          as: 'category',
          attributes: ['name', 'code']
        },
        {
          model: Department,
          as: 'department',
          attributes: ['name']
        },
        {
          model: Receipt,
          as: 'receipts'
        },
        {
          model: Approval,
          as: 'approvals',
          include: [{
            model: User,
            as: 'approver',
            attributes: ['firstName', 'lastName', 'email']
          }]
        }
      ]
    });

    if (!expense) {
      throw ApiError.notFound('Expense not found');
    }

    res.status(200).json({
      status: 'success',
      data: expense
    });
  } catch (error) {
    logger.error('Error getting expense by ID:', error);
    next(error);
  }
};

/**
 * Create a new expense
 */
const createExpense = async (req, res, next) => {
  try {
    const {
      amount,
      currency = 'USD',
      description,
      expenseDate,
      merchant,
      categoryId,
      departmentId,
      projectId,
      paymentMethod = 'personal_card',
      isReimbursable = true,
      notes
    } = req.body;

    // Convert to base currency if needed
    let amountInBaseCurrency = amount;
    let exchangeRate = 1.0;
    
    if (currency !== 'USD') {
      const conversion = await currencyService.convertCurrency(amount, currency, 'USD');
      amountInBaseCurrency = conversion.convertedAmount;
      exchangeRate = conversion.rate;
    }

    const expense = await Expense.create({
      userId: req.user.id,
      companyId: req.user.companyId,
      amount,
      currency,
      exchangeRate,
      amountInBaseCurrency,
      description,
      expenseDate,
      merchant,
      categoryId,
      departmentId,
      projectId,
      paymentMethod,
      isReimbursable,
      notes,
      status: 'draft'
    });

    // Log audit event
    await auditService.logEvent({
      entityType: 'expense',
      entityId: expense.id,
      action: 'create',
      changes: expense.toJSON(),
      userId: req.user.id,
      ipAddress: req.ip,
      userAgent: req.get('User-Agent')
    });

    // Get expense with associations for response
    const createdExpense = await Expense.findByPk(expense.id, {
      include: [
        {
          model: Category,
          as: 'category',
          attributes: ['name', 'code']
        },
        {
          model: Department,
          as: 'department',
          attributes: ['name']
        }
      ]
    });

    res.status(201).json({
      status: 'success',
      data: createdExpense
    });
  } catch (error) {
    logger.error('Error creating expense:', error);
    next(error);
  }
};

/**
 * Update an expense
 */
const updateExpense = async (req, res, next) => {
  try {
    const { id } = req.params;
    const updateData = req.body;

    const where = { id };
    
    // Apply company filter
    if (req.user.companyId) {
      where.companyId = req.user.companyId;
    }
    
    // Apply user filter for non-admin users
    if (req.user.role === 'employee') {
      where.userId = req.user.id;
    }

    const expense = await Expense.findOne({ where });

    if (!expense) {
      throw ApiError.notFound('Expense not found');
    }

    // Check if expense can be updated
    if (expense.status === 'approved' || expense.status === 'reimbursed') {
      throw ApiError.badRequest('Cannot update approved or reimbursed expenses');
    }

    const oldData = expense.toJSON();

    // Handle currency conversion if currency changed
    if (updateData.currency && updateData.currency !== expense.currency) {
      const conversion = await currencyService.convertCurrency(
        updateData.amount || expense.amount, 
        updateData.currency, 
        'USD'
      );
      updateData.amountInBaseCurrency = conversion.convertedAmount;
      updateData.exchangeRate = conversion.rate;
    } else if (updateData.amount && updateData.amount !== expense.amount) {
      // Recalculate base currency amount if amount changed
      const conversion = await currencyService.convertCurrency(
        updateData.amount, 
        expense.currency, 
        'USD'
      );
      updateData.amountInBaseCurrency = conversion.convertedAmount;
    }

    await expense.update(updateData);

    // Log audit event
    await auditService.logEvent({
      entityType: 'expense',
      entityId: expense.id,
      action: 'update',
      changes: { from: oldData, to: expense.toJSON() },
      userId: req.user.id,
      ipAddress: req.ip,
      userAgent: req.get('User-Agent')
    });

    // Get updated expense with associations
    const updatedExpense = await Expense.findByPk(expense.id, {
      include: [
        {
          model: Category,
          as: 'category',
          attributes: ['name', 'code']
        },
        {
          model: Department,
          as: 'department',
          attributes: ['name']
        }
      ]
    });

    res.status(200).json({
      status: 'success',
      data: updatedExpense
    });
  } catch (error) {
    logger.error('Error updating expense:', error);
    next(error);
  }
};

/**
 * Delete an expense
 */
const deleteExpense = async (req, res, next) => {
  try {
    const { id } = req.params;

    const where = { id };
    
    // Apply company filter
    if (req.user.companyId) {
      where.companyId = req.user.companyId;
    }
    
    // Apply user filter for non-admin users
    if (req.user.role === 'employee') {
      where.userId = req.user.id;
    }

    const expense = await Expense.findOne({ where });

    if (!expense) {
      throw ApiError.notFound('Expense not found');
    }

    // Check if expense can be deleted
    if (expense.status === 'approved' || expense.status === 'reimbursed') {
      throw ApiError.badRequest('Cannot delete approved or reimbursed expenses');
    }

    await expense.destroy();

    // Log audit event
    await auditService.logEvent({
      entityType: 'expense',
      entityId: expense.id,
      action: 'delete',
      changes: { deleted: expense.toJSON() },
      userId: req.user.id,
      ipAddress: req.ip,
      userAgent: req.get('User-Agent')
    });

    res.status(200).json({
      status: 'success',
      message: 'Expense deleted successfully'
    });
  } catch (error) {
    logger.error('Error deleting expense:', error);
    next(error);
  }
};

/**
 * Upload receipt for an expense
 */
const uploadReceipt = async (req, res, next) => {
  try {
    const { expenseId } = req.body;

    if (!req.file) {
      throw ApiError.badRequest('No file uploaded');
    }

    const expense = await Expense.findOne({
      where: {
        id: expenseId,
        companyId: req.user.companyId,
        ...(req.user.role === 'employee' && { userId: req.user.id })
      }
    });

    if (!expense) {
      throw ApiError.notFound('Expense not found');
    }

    // Create file upload record
    const fileUpload = await FileUpload.create({
      fileName: req.file.filename,
      originalName: req.file.originalname,
      fileType: req.file.mimetype,
      fileSize: req.file.size,
      filePath: req.file.path,
      uploadedBy: req.user.id,
      companyId: req.user.companyId,
      entityType: 'expense',
      entityId: expenseId
    });

    // Create receipt record
    const receipt = await Receipt.create({
      expenseId,
      fileUrl: req.file.path,
      fileName: req.file.originalname,
      fileType: req.file.mimetype,
      fileSize: req.file.size,
      uploadedBy: req.user.id
    });

    // Try to extract data using OCR
    try {
      const ocrResult = await aiService.extractReceiptData(req.file.path);
      if (ocrResult.success) {
        await receipt.update({
          ocrData: ocrResult.data,
          ocrConfidence: ocrResult.data.confidence,
          extractedAmount: ocrResult.data.amount,
          extractedDate: ocrResult.data.date,
          extractedMerchant: ocrResult.data.merchant,
          isProcessed: true,
          ocrProcessedAt: new Date()
        });
      }
    } catch (ocrError) {
      logger.warn('OCR processing failed:', ocrError);
    }

    res.status(200).json({
      status: 'success',
      data: {
        receipt,
        fileUpload
      }
    });
  } catch (error) {
    logger.error('Error uploading receipt:', error);
    next(error);
  }
};

/**
 * Submit expense for approval
 */
const submitExpense = async (req, res, next) => {
  try {
    const { expenseId, notes } = req.body;

    const expense = await Expense.findOne({
      where: {
        id: expenseId,
        companyId: req.user.companyId,
        userId: req.user.id
      }
    });

    if (!expense) {
      throw ApiError.notFound('Expense not found');
    }

    if (expense.status !== 'draft') {
      throw ApiError.badRequest('Expense has already been submitted');
    }

    // Update expense status
    await expense.update({
      status: 'submitted',
      notes: notes || expense.notes
    });

    // Create approval workflow (simplified - would be more complex in real system)
    const approval = await Approval.create({
      expenseId: expense.id,
      approverId: req.user.departmentId ? 
        await getDepartmentManager(req.user.departmentId) : 
        await getDefaultApprover(req.user.companyId),
      status: 'pending',
      level: 1,
      order: 1
    });

    // Send notification to approver
    await notificationService.createExpenseNotification(expense, 'submitted');

    // Log audit event
    await auditService.logEvent({
      entityType: 'expense',
      entityId: expense.id,
      action: 'submit',
      changes: { status: 'submitted', approvalId: approval.id },
      userId: req.user.id,
      ipAddress: req.ip,
      userAgent: req.get('User-Agent')
    });

    res.status(200).json({
      status: 'success',
      message: 'Expense submitted for approval',
      data: {
        expense,
        approval
      }
    });
  } catch (error) {
    logger.error('Error submitting expense:', error);
    next(error);
  }
};

/**
 * Get expenses pending approval
 */
const getPendingApprovalExpenses = async (req, res, next) => {
  try {
    const expenses = await Expense.findAll({
      where: {
        companyId: req.user.companyId,
        status: 'submitted'
      },
      include: [
        {
          model: User,
          as: 'user',
          attributes: ['firstName', 'lastName', 'email']
        },
        {
          model: Category,
          as: 'category',
          attributes: ['name']
        },
        {
          model: Approval,
          as: 'approvals',
          where: {
            approverId: req.user.id,
            status: 'pending'
          }
        }
      ],
      order: [['createdAt', 'ASC']]
    });

    res.status(200).json({
      status: 'success',
      data: expenses
    });
  } catch (error) {
    logger.error('Error getting pending approval expenses:', error);
    next(error);
  }
};

/**
 * Bulk categorize expenses using AI
 */
const bulkCategorizeExpenses = async (req, res, next) => {
  try {
    const { expenseIds } = req.body;

    if (!expenseIds || !Array.isArray(expenseIds)) {
      throw ApiError.badRequest('Expense IDs array is required');
    }

    const expenses = await Expense.findAll({
      where: {
        id: { [Op.in]: expenseIds },
        companyId: req.user.companyId,
        status: 'draft'
      }
    });

    const results = [];

    for (const expense of expenses) {
      try {
        const categorization = await aiService.categorizeExpense({
          description: expense.description,
          merchant: expense.merchant,
          amount: expense.amount
        });

        // Find category by name
        const category = await Category.findOne({
          where: { name: categorization.category }
        });

        if (category) {
          await expense.update({ categoryId: category.id });
          results.push({
            expenseId: expense.id,
            success: true,
            category: categorization.category,
            confidence: categorization.confidence
          });
        } else {
          results.push({
            expenseId: expense.id,
            success: false,
            error: 'Category not found'
          });
        }
      } catch (error) {
        results.push({
          expenseId: expense.id,
          success: false,
          error: error.message
        });
      }
    }

    res.status(200).json({
      status: 'success',
      data: results
    });
  } catch (error) {
    logger.error('Error bulk categorizing expenses:', error);
    next(error);
  }
};

/**
 * Get potential duplicate expenses
 */
const getDuplicateCandidates = async (req, res, next) => {
  try {
    // Find expenses with similar amounts and dates
    const duplicates = await Expense.findAll({
      where: {
        companyId: req.user.companyId,
        status: { [Op.in]: ['draft', 'submitted'] }
      },
      include: [
        {
          model: User,
          as: 'user',
          attributes: ['firstName', 'lastName']
        }
      ],
      order: [['expenseDate', 'DESC'], ['amount', 'DESC']]
    });

    // Group by similar criteria
    const groups = {};
    
    duplicates.forEach(expense => {
      const key = `${expense.expenseDate}_${Math.round(expense.amount)}_${expense.merchant || 'unknown'}`;
      if (!groups[key]) {
        groups[key] = [];
      }
      groups[key].push(expense);
    });

    // Filter groups with more than one expense
    const duplicateGroups = Object.values(groups).filter(group => group.length > 1);

    res.status(200).json({
      status: 'success',
      data: duplicateGroups
    });
  } catch (error) {
    logger.error('Error getting duplicate candidates:', error);
    next(error);
  }
};

// Helper functions

/**
 * Get department manager for approval
 */
const getDepartmentManager = async (departmentId) => {
  const department = await Department.findByPk(departmentId);
  return department?.managerId;
};

/**
 * Get default approver for company
 */
const getDefaultApprover = async (companyId) => {
  const admin = await User.findOne({
    where: {
      companyId,
      role: { [Op.in]: ['admin', 'manager'] },
      isActive: true
    }
  });
  return admin?.id;
};

module.exports = {
  getAllExpenses,
  getExpenseById,
  createExpense,
  updateExpense,
  deleteExpense,
  uploadReceipt,
  submitExpense,
  getPendingApprovalExpenses,
  bulkCategorizeExpenses,
  getDuplicateCandidates
};