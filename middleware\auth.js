const jwt = require('jsonwebtoken');
const { pool } = require('../config/database');
const logger = require('../utils/logger');

const authenticateToken = async (req, res, next) => {
    const authHeader = req.headers['authorization'];
    const token = authHeader && authHeader.split(' ')[1];

    if (!token) {
        return res.status(401).json({ error: 'Access token required' });
    }

    try {
        const decoded = jwt.verify(token, process.env.JWT_SECRET);
        
        // Verify user still exists
        const result = await pool.query(
            'SELECT id, email, subscription_tier, subscription_expires_at FROM users WHERE id = $1',
            [decoded.userId]
        );

        if (result.rows.length === 0) {
            return res.status(401).json({ error: 'User not found' });
        }

        req.user = result.rows[0];
        req.userId = decoded.userId;
        next();
    } catch (error) {
        logger.error('Token verification failed:', error);
        return res.status(403).json({ error: 'Invalid or expired token' });
    }
};

const requirePremium = (feature) => {
    return async (req, res, next) => {
        const user = req.user;
        
        if (user.subscription_tier !== 'premium' || 
            (user.subscription_expires_at && user.subscription_expires_at < new Date())) {
            return res.status(403).json({
                error: 'Premium subscription required',
                feature: feature,
                upgrade_url: '/api/user/upgrade-premium'
            });
        }
        
        next();
    };
};

module.exports = {
    authenticateToken,
    requirePremium
};