'use strict';

const startupFinanceService = require('./startupFinanceService');
const { Company } = require('../models');
const logger = require('../utils/logger');

/**
 * Background tasks for startup finance calculations
 */
class StartupTaskService {
  
  /**
   * Update cash positions for all companies
   */
  async updateAllCashPositions() {
    try {
      logger.info('Starting cash position updates for all companies');
      
      const companies = await Company.findAll({
        where: { status: 'active' }
      });
      
      let updated = 0;
      let errors = 0;
      
      for (const company of companies) {
        try {
          await startupFinanceService.getCurrentCashPosition(company.id);
          updated++;
        } catch (error) {
          logger.error(`Error updating cash position for company ${company.id}:`, error);
          errors++;
        }
      }
      
      logger.info(`Cash position update completed: ${updated} updated, ${errors} errors`);
      return { updated, errors };
    } catch (error) {
      logger.error('Error in updateAllCashPositions:', error);
      throw error;
    }
  }
  
  /**
   * Calculate burn rates for all companies
   */
  async calculateAllBurnRates() {
    try {
      logger.info('Starting burn rate calculations for all companies');
      
      const companies = await Company.findAll({
        where: { status: 'active' }
      });
      
      let calculated = 0;
      let errors = 0;
      
      for (const company of companies) {
        try {
          await startupFinanceService.calculateMonthlyBurnRate(company.id);
          calculated++;
        } catch (error) {
          logger.error(`Error calculating burn rate for company ${company.id}:`, error);
          errors++;
        }
      }
      
      logger.info(`Burn rate calculation completed: ${calculated} calculated, ${errors} errors`);
      return { calculated, errors };
    } catch (error) {
      logger.error('Error in calculateAllBurnRates:', error);
      throw error;
    }
  }
  
  /**
   * Generate runway projections for all companies
   */
  async generateAllRunwayProjections() {
    try {
      logger.info('Starting runway projections for all companies');
      
      const companies = await Company.findAll({
        where: { status: 'active' }
      });
      
      let generated = 0;
      let errors = 0;
      
      for (const company of companies) {
        try {
          await startupFinanceService.generateCashFlowProjections(company.id, 6);
          generated++;
        } catch (error) {
          logger.error(`Error generating projections for company ${company.id}:`, error);
          errors++;
        }
      }
      
      logger.info(`Runway projections completed: ${generated} generated, ${errors} errors`);
      return { generated, errors };
    } catch (error) {
      logger.error('Error in generateAllRunwayProjections:', error);
      throw error;
    }
  }
  
  /**
   * Check financial health for all companies
   */
  async checkAllFinancialHealth() {
    try {
      logger.info('Starting financial health checks for all companies');
      
      const companies = await Company.findAll({
        where: { status: 'active' }
      });
      
      let checked = 0;
      let alertsCreated = 0;
      let errors = 0;
      
      for (const company of companies) {
        try {
          const alerts = await startupFinanceService.checkRunwayAlerts(company.id);
          alertsCreated += alerts.length;
          checked++;
        } catch (error) {
          logger.error(`Error checking health for company ${company.id}:`, error);
          errors++;
        }
      }
      
      logger.info(`Financial health check completed: ${checked} checked, ${alertsCreated} alerts created, ${errors} errors`);
      return { checked, alertsCreated, errors };
    } catch (error) {
      logger.error('Error in checkAllFinancialHealth:', error);
      throw error;
    }
  }
  
  /**
   * Run daily startup finance tasks
   */
  async runDailyTasks() {
    try {
      logger.info('Starting daily startup finance tasks');
      
      const results = await Promise.allSettled([
        this.updateAllCashPositions(),
        this.calculateAllBurnRates(),
        this.checkAllFinancialHealth()
      ]);
      
      const summary = {
        cashPositions: results[0].status === 'fulfilled' ? results[0].value : { error: results[0].reason },
        burnRates: results[1].status === 'fulfilled' ? results[1].value : { error: results[1].reason },
        healthChecks: results[2].status === 'fulfilled' ? results[2].value : { error: results[2].reason }
      };
      
      logger.info('Daily startup finance tasks completed:', summary);
      return summary;
    } catch (error) {
      logger.error('Error in runDailyTasks:', error);
      throw error;
    }
  }
  
  /**
   * Run weekly startup finance tasks
   */
  async runWeeklyTasks() {
    try {
      logger.info('Starting weekly startup finance tasks');
      
      const results = await Promise.allSettled([
        this.generateAllRunwayProjections()
      ]);
      
      const summary = {
        projections: results[0].status === 'fulfilled' ? results[0].value : { error: results[0].reason }
      };
      
      logger.info('Weekly startup finance tasks completed:', summary);
      return summary;
    } catch (error) {
      logger.error('Error in runWeeklyTasks:', error);
      throw error;
    }
  }
}

module.exports = new StartupTaskService();