const Queue = require('bull');
const redis = require('redis');
const logger = require('../utils/logger');

// Redis connection for Bull
const redisConfig = {
    host: process.env.REDIS_HOST || 'localhost',
    port: process.env.REDIS_PORT || 6379,
    password: process.env.REDIS_PASSWORD,
    maxRetriesPerRequest: 3,
    retryDelayOnFailover: 100,
    enableReadyCheck: false,
    maxRetriesPerRequest: null
};

// Create queues
const statementProcessingQueue = new Queue('statement processing', {
    redis: redisConfig,
    defaultJobOptions: {
        removeOnComplete: 10,
        removeOnFail: 50,
        attempts: 3,
        backoff: {
            type: 'exponential',
            delay: 2000
        }
    }
});

const aiCategorizationQueue = new Queue('ai categorization', {
    redis: redisConfig,
    defaultJobOptions: {
        removeOnComplete: 5,
        removeOnFail: 25,
        attempts: 2,
        backoff: {
            type: 'exponential',
            delay: 5000
        }
    }
});

const importQueue = new Queue('transaction import', {
    redis: redisConfig,
    defaultJobOptions: {
        removeOnComplete: 5,
        removeOnFail: 25,
        attempts: 3,
        backoff: {
            type: 'exponential',
            delay: 1000
        }
    }
});

// Queue event handlers
statementProcessingQueue.on('completed', (job, result) => {
    logger.info(`Statement processing job ${job.id} completed`);
});

statementProcessingQueue.on('failed', (job, err) => {
    logger.error(`Statement processing job ${job.id} failed:`, err);
});

aiCategorizationQueue.on('completed', (job, result) => {
    logger.info(`AI categorization job ${job.id} completed`);
});

aiCategorizationQueue.on('failed', (job, err) => {
    logger.error(`AI categorization job ${job.id} failed:`, err);
});

importQueue.on('completed', (job, result) => {
    logger.info(`Import job ${job.id} completed`);
});

importQueue.on('failed', (job, err) => {
    logger.error(`Import job ${job.id} failed:`, err);
});

// Graceful shutdown
process.on('SIGTERM', async () => {
    logger.info('Closing queues...');
    await statementProcessingQueue.close();
    await aiCategorizationQueue.close();
    await importQueue.close();
});

module.exports = {
    statementProcessingQueue,
    aiCategorizationQueue,
    importQueue
};