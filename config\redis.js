const redis = require('redis');
const logger = require('../utils/logger');

let client;

const connectRedis = async () => {
    try {
        client = redis.createClient({
            host: process.env.REDIS_HOST || 'localhost',
            port: process.env.REDIS_PORT || 6379,
            password: process.env.REDIS_PASSWORD,
        });

        client.on('error', (err) => {
            logger.error('Redis Client Error:', err);
        });

        client.on('connect', () => {
            logger.info('Redis connected successfully');
        });

        await client.connect();
        return client;
    } catch (error) {
        logger.error('Redis connection failed:', error);
        // Continue without Redis for development
        return null;
    }
};

const getRedisClient = () => client;

module.exports = {
    connectRedis,
    getRedisClient
};