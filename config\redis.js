const redis = require('redis');
const logger = require('../utils/logger');

let client;

// Mock Redis client for development
const createMockRedisClient = () => {
    const mockData = new Map();

    return {
        get: async (key) => mockData.get(key) || null,
        set: async (key, value, options) => {
            mockData.set(key, value);
            return 'OK';
        },
        del: async (key) => {
            const existed = mockData.has(key);
            mockData.delete(key);
            return existed ? 1 : 0;
        },
        exists: async (key) => mockData.has(key) ? 1 : 0,
        expire: async (key, seconds) => {
            // Mock implementation - in real Redis this would set TTL
            return mockData.has(key) ? 1 : 0;
        },
        flushAll: async () => {
            mockData.clear();
            return 'OK';
        },
        quit: async () => 'OK',
        disconnect: () => {},
        isReady: true,
        // Hash operations
        hGet: async (hash, field) => null,
        hSet: async (hash, field, value) => 1,
        hDel: async (hash, field) => 0,
        hGetAll: async (hash) => ({}),
        // List operations
        lPush: async (key, ...values) => values.length,
        rPop: async (key) => null,
        lLen: async (key) => 0,
        // Set operations
        sAdd: async (key, ...members) => members.length,
        sMembers: async (key) => [],
        // Other operations
        keys: async (pattern) => [],
        incr: async (key) => {
            const current = parseInt(mockData.get(key) || '0');
            const newValue = current + 1;
            mockData.set(key, newValue.toString());
            return newValue;
        },
        decr: async (key) => {
            const current = parseInt(mockData.get(key) || '0');
            const newValue = current - 1;
            mockData.set(key, newValue.toString());
            return newValue;
        }
    };
};

const connectRedis = async () => {
    // Use mock Redis for development
    if (process.env.NODE_ENV === 'development') {
        logger.info('Using mock Redis client for development');
        client = createMockRedisClient();
        return client;
    }

    // Use real Redis for production
    try {
        client = redis.createClient({
            host: process.env.REDIS_HOST || 'localhost',
            port: process.env.REDIS_PORT || 6379,
            password: process.env.REDIS_PASSWORD,
        });

        client.on('error', (err) => {
            logger.error('Redis Client Error:', err);
        });

        client.on('connect', () => {
            logger.info('Redis connected successfully');
        });

        await client.connect();
        return client;
    } catch (error) {
        logger.error('Redis connection failed, falling back to mock Redis:', error);
        client = createMockRedisClient();
        return client;
    }
};

const getRedisClient = () => client;

module.exports = {
    connectRedis,
    getRedisClient
};