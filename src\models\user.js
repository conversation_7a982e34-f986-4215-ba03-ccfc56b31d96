'use strict';

const bcrypt = require('bcryptjs');

module.exports = (sequelize, DataTypes) => {
  const User = sequelize.define('User', {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true
    },
    firstName: {
      type: DataTypes.STRING,
      allowNull: false,
      validate: {
        notEmpty: true
      }
    },
    lastName: {
      type: DataTypes.STRING,
      allowNull: false,
      validate: {
        notEmpty: true
      }
    },
    email: {
      type: DataTypes.STRING,
      allowNull: false,
      unique: true,
      validate: {
        isEmail: true
      }
    },
    password: {
      type: DataTypes.STRING,
      allowNull: false,
      validate: {
        len: [6, 100]
      }
    },
    role: {
      type: DataTypes.ENUM('admin', 'manager', 'employee', 'accountant'),
      defaultValue: 'employee'
    },
    isActive: {
      type: DataTypes.BOOLEAN,
      defaultValue: true
    },
    lastLogin: {
      type: DataTypes.DATE
    },
    companyId: {
      type: DataTypes.UUID,
      references: {
        model: 'Companies',
        key: 'id'
      }
    },
    departmentId: {
      type: DataTypes.UUID,
      references: {
        model: 'Departments',
        key: 'id'
      }
    }
  }, {
    timestamps: true,
    paranoid: true, // Soft deletes
    hooks: {
      beforeCreate: async (user) => {
        if (user.password) {
          user.password = await bcrypt.hash(user.password, 10);
        }
      },
      beforeUpdate: async (user) => {
        if (user.changed('password')) {
          user.password = await bcrypt.hash(user.password, 10);
        }
      }
    }
  });

  User.prototype.comparePassword = async function(candidatePassword) {
    return await bcrypt.compare(candidatePassword, this.password);
  };

  User.associate = (models) => {
    User.belongsTo(models.Company, {
      foreignKey: 'companyId',
      as: 'company'
    });
    
    User.belongsTo(models.Department, {
      foreignKey: 'departmentId',
      as: 'department'
    });
    
    User.hasMany(models.Expense, {
      foreignKey: 'userId',
      as: 'expenses'
    });
    
    User.hasMany(models.Approval, {
      foreignKey: 'approverId',
      as: 'approvals'
    });

    User.hasOne(models.TwoFactorAuth, {
      foreignKey: 'userId',
      as: 'twoFactorAuth'
    });

    User.hasMany(models.UserSession, {
      foreignKey: 'userId',
      as: 'sessions'
    });

    User.hasMany(models.Notification, {
      foreignKey: 'userId',
      as: 'notifications'
    });

    User.hasMany(models.AuditLog, {
      foreignKey: 'userId',
      as: 'auditLogs'
    });
  };

  return User;
};