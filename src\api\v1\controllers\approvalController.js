'use strict';

const { Op } = require('sequelize');
const { Approval, Expense, User, Category, Company, Department } = require('../../../models');
const { ApiError } = require('../../../utils/apiError');
const auditService = require('../../../services/auditService');
const notificationService = require('../../../services/notificationService');
const logger = require('../../../utils/logger');

/**
 * Get all approvals for the current user
 */
const getApprovals = async (req, res, next) => {
  try {
    const {
      page = 1,
      limit = 20,
      status,
      sortBy = 'createdAt',
      order = 'DESC'
    } = req.query;

    const where = { approverId: req.user.id };
    
    if (status) {
      where.status = status;
    }

    const offset = (page - 1) * limit;

    const { count, rows } = await Approval.findAndCountAll({
      where,
      include: [
        {
          model: Expense,
          as: 'expense',
          include: [
            {
              model: User,
              as: 'user',
              attributes: ['firstName', 'lastName', 'email']
            },
            {
              model: Category,
              as: 'category',
              attributes: ['name']
            }
          ]
        }
      ],
      order: [[sortBy, order.toUpperCase()]],
      limit: parseInt(limit),
      offset
    });

    res.status(200).json({
      status: 'success',
      data: {
        approvals: rows,
        pagination: {
          total: count,
          page: parseInt(page),
          limit: parseInt(limit),
          totalPages: Math.ceil(count / limit)
        }
      }
    });
  } catch (error) {
    logger.error('Error getting approvals:', error);
    next(error);
  }
};

/**
 * Get approval by ID
 */
const getApprovalById = async (req, res, next) => {
  try {
    const { id } = req.params;

    const approval = await Approval.findOne({
      where: { 
        id,
        approverId: req.user.id 
      },
      include: [
        {
          model: Expense,
          as: 'expense',
          include: [
            {
              model: User,
              as: 'user',
              attributes: ['firstName', 'lastName', 'email']
            },
            {
              model: Category,
              as: 'category',
              attributes: ['name']
            },
            {
              model: Department,
              as: 'department',
              attributes: ['name']
            }
          ]
        }
      ]
    });

    if (!approval) {
      throw ApiError.notFound('Approval not found');
    }

    res.status(200).json({
      status: 'success',
      data: approval
    });
  } catch (error) {
    logger.error('Error getting approval by ID:', error);
    next(error);
  }
};

/**
 * Approve an expense
 */
const approveExpense = async (req, res, next) => {
  try {
    const { id } = req.params;
    const { comments } = req.body;

    const approval = await Approval.findOne({
      where: { 
        id,
        approverId: req.user.id,
        status: 'pending'
      },
      include: [{
        model: Expense,
        as: 'expense'
      }]
    });

    if (!approval) {
      throw ApiError.notFound('Pending approval not found');
    }

    // Update approval
    approval.status = 'approved';
    approval.comments = comments;
    approval.actionDate = new Date();
    await approval.save();

    // Update expense status
    const expense = approval.expense;
    expense.status = 'approved';
    await expense.save();

    // Log audit event
    await auditService.logApprovalEvent(
      approval,
      'approve',
      { status: 'approved', comments },
      req.user,
      req
    );

    // Create notification for expense submitter
    await notificationService.createExpenseNotification(expense, 'approved');

    res.status(200).json({
      status: 'success',
      message: 'Expense approved successfully',
      data: approval
    });
  } catch (error) {
    logger.error('Error approving expense:', error);
    next(error);
  }
};

/**
 * Reject an expense
 */
const rejectExpense = async (req, res, next) => {
  try {
    const { id } = req.params;
    const { comments } = req.body;

    if (!comments) {
      throw ApiError.badRequest('Comments are required for rejection');
    }

    const approval = await Approval.findOne({
      where: { 
        id,
        approverId: req.user.id,
        status: 'pending'
      },
      include: [{
        model: Expense,
        as: 'expense'
      }]
    });

    if (!approval) {
      throw ApiError.notFound('Pending approval not found');
    }

    // Update approval
    approval.status = 'rejected';
    approval.comments = comments;
    approval.actionDate = new Date();
    await approval.save();

    // Update expense status
    const expense = approval.expense;
    expense.status = 'rejected';
    await expense.save();

    // Log audit event
    await auditService.logApprovalEvent(
      approval,
      'reject',
      { status: 'rejected', comments },
      req.user,
      req
    );

    // Create notification for expense submitter
    await notificationService.createExpenseNotification(expense, 'rejected');

    res.status(200).json({
      status: 'success',
      message: 'Expense rejected successfully',
      data: approval
    });
  } catch (error) {
    logger.error('Error rejecting expense:', error);
    next(error);
  }
};

/**
 * Escalate an approval
 */
const escalateApproval = async (req, res, next) => {
  try {
    const { id } = req.params;
    const { reason, escalateTo } = req.body;

    const approval = await Approval.findOne({
      where: { 
        id,
        approverId: req.user.id,
        status: 'pending'
      },
      include: [{
        model: Expense,
        as: 'expense'
      }]
    });

    if (!approval) {
      throw ApiError.notFound('Pending approval not found');
    }

    // Update current approval
    approval.status = 'escalated';
    approval.comments = reason;
    approval.actionDate = new Date();
    await approval.save();

    // Create new approval for escalated user
    const newApproval = await Approval.create({
      expenseId: approval.expenseId,
      approverId: escalateTo || await getNextApprover(req.user.id),
      level: approval.level + 1,
      order: approval.order + 1,
      isEscalation: true,
      escalatedFrom: req.user.id
    });

    // Log audit event
    await auditService.logApprovalEvent(
      approval,
      'escalate',
      { status: 'escalated', reason, escalatedTo: newApproval.approverId },
      req.user,
      req
    );

    res.status(200).json({
      status: 'success',
      message: 'Approval escalated successfully',
      data: { originalApproval: approval, newApproval }
    });
  } catch (error) {
    logger.error('Error escalating approval:', error);
    next(error);
  }
};

/**
 * Get pending approvals
 */
const getPendingApprovals = async (req, res, next) => {
  try {
    const approvals = await Approval.findAll({
      where: { 
        approverId: req.user.id,
        status: 'pending'
      },
      include: [
        {
          model: Expense,
          as: 'expense',
          include: [
            {
              model: User,
              as: 'user',
              attributes: ['firstName', 'lastName', 'email']
            },
            {
              model: Category,
              as: 'category',
              attributes: ['name']
            }
          ]
        }
      ],
      order: [['createdAt', 'ASC']]
    });

    res.status(200).json({
      status: 'success',
      data: approvals
    });
  } catch (error) {
    logger.error('Error getting pending approvals:', error);
    next(error);
  }
};

/**
 * Bulk approve expenses
 */
const bulkApprove = async (req, res, next) => {
  try {
    const { approvalIds, comments } = req.body;

    if (!approvalIds || !Array.isArray(approvalIds)) {
      throw ApiError.badRequest('Approval IDs array is required');
    }

    const results = { success: 0, failed: 0, errors: [] };

    for (const approvalId of approvalIds) {
      try {
        const approval = await Approval.findOne({
          where: { 
            id: approvalId,
            approverId: req.user.id,
            status: 'pending'
          },
          include: [{
            model: Expense,
            as: 'expense'
          }]
        });

        if (approval) {
          // Update approval
          approval.status = 'approved';
          approval.comments = comments;
          approval.actionDate = new Date();
          await approval.save();

          // Update expense
          approval.expense.status = 'approved';
          await approval.expense.save();

          // Log audit event
          await auditService.logApprovalEvent(
            approval,
            'bulk_approve',
            { status: 'approved', comments },
            req.user,
            req
          );

          // Create notification
          await notificationService.createExpenseNotification(approval.expense, 'approved');

          results.success++;
        } else {
          results.failed++;
          results.errors.push(`Approval ${approvalId} not found or not pending`);
        }
      } catch (error) {
        results.failed++;
        results.errors.push(`Error processing approval ${approvalId}: ${error.message}`);
      }
    }

    res.status(200).json({
      status: 'success',
      message: 'Bulk approval completed',
      data: results
    });
  } catch (error) {
    logger.error('Error in bulk approve:', error);
    next(error);
  }
};

/**
 * Bulk reject expenses
 */
const bulkReject = async (req, res, next) => {
  try {
    const { approvalIds, comments } = req.body;

    if (!approvalIds || !Array.isArray(approvalIds)) {
      throw ApiError.badRequest('Approval IDs array is required');
    }

    if (!comments) {
      throw ApiError.badRequest('Comments are required for bulk rejection');
    }

    const results = { success: 0, failed: 0, errors: [] };

    for (const approvalId of approvalIds) {
      try {
        const approval = await Approval.findOne({
          where: { 
            id: approvalId,
            approverId: req.user.id,
            status: 'pending'
          },
          include: [{
            model: Expense,
            as: 'expense'
          }]
        });

        if (approval) {
          // Update approval
          approval.status = 'rejected';
          approval.comments = comments;
          approval.actionDate = new Date();
          await approval.save();

          // Update expense
          approval.expense.status = 'rejected';
          await approval.expense.save();

          // Log audit event
          await auditService.logApprovalEvent(
            approval,
            'bulk_reject',
            { status: 'rejected', comments },
            req.user,
            req
          );

          // Create notification
          await notificationService.createExpenseNotification(approval.expense, 'rejected');

          results.success++;
        } else {
          results.failed++;
          results.errors.push(`Approval ${approvalId} not found or not pending`);
        }
      } catch (error) {
        results.failed++;
        results.errors.push(`Error processing approval ${approvalId}: ${error.message}`);
      }
    }

    res.status(200).json({
      status: 'success',
      message: 'Bulk rejection completed',
      data: results
    });
  } catch (error) {
    logger.error('Error in bulk reject:', error);
    next(error);
  }
};

/**
 * Get approval statistics
 */
const getApprovalStats = async (req, res, next) => {
  try {
    const userId = req.user.id;

    const [
      totalApprovals,
      pendingCount,
      approvedCount,
      rejectedCount,
      avgProcessingTime,
      recentActivity
    ] = await Promise.all([
      Approval.count({ where: { approverId: userId } }),
      Approval.count({ where: { approverId: userId, status: 'pending' } }),
      Approval.count({ where: { approverId: userId, status: 'approved' } }),
      Approval.count({ where: { approverId: userId, status: 'rejected' } }),
      calculateAverageProcessingTime(userId),
      getRecentApprovalActivity(userId)
    ]);

    const stats = {
      total: totalApprovals,
      pending: pendingCount,
      approved: approvedCount,
      rejected: rejectedCount,
      averageProcessingTime: avgProcessingTime,
      recentActivity,
      approvalRate: totalApprovals > 0 ? (approvedCount / totalApprovals * 100).toFixed(2) : 0
    };

    res.status(200).json({
      status: 'success',
      data: stats
    });
  } catch (error) {
    logger.error('Error getting approval stats:', error);
    next(error);
  }
};

// Helper functions

/**
 * Get next approver in hierarchy
 */
const getNextApprover = async (currentApproverId) => {
  // This would implement your approval hierarchy logic
  // For now, return a manager or admin
  const manager = await User.findOne({
    where: { role: 'manager' },
    order: [['createdAt', 'ASC']]
  });
  
  return manager ? manager.id : currentApproverId;
};

/**
 * Calculate average processing time
 */
const calculateAverageProcessingTime = async (userId) => {
  try {
    const processedApprovals = await Approval.findAll({
      where: {
        approverId: userId,
        status: { [Op.in]: ['approved', 'rejected'] },
        actionDate: { [Op.not]: null }
      },
      attributes: ['createdAt', 'actionDate']
    });

    if (processedApprovals.length === 0) {
      return 0;
    }

    const totalTime = processedApprovals.reduce((sum, approval) => {
      const processingTime = new Date(approval.actionDate) - new Date(approval.createdAt);
      return sum + processingTime;
    }, 0);

    // Return average time in hours
    return Math.round((totalTime / processedApprovals.length) / (1000 * 60 * 60) * 100) / 100;
  } catch (error) {
    logger.error('Error calculating average processing time:', error);
    return 0;
  }
};

/**
 * Get recent approval activity
 */
const getRecentApprovalActivity = async (userId) => {
  try {
    const recentApprovals = await Approval.findAll({
      where: {
        approverId: userId,
        actionDate: { [Op.not]: null }
      },
      include: [{
        model: Expense,
        as: 'expense',
        include: [{
          model: User,
          as: 'user',
          attributes: ['firstName', 'lastName']
        }]
      }],
      order: [['actionDate', 'DESC']],
      limit: 10
    });

    return recentApprovals.map(approval => ({
      id: approval.id,
      action: approval.status,
      expenseAmount: approval.expense.amountInBaseCurrency,
      submittedBy: `${approval.expense.user.firstName} ${approval.expense.user.lastName}`,
      actionDate: approval.actionDate
    }));
  } catch (error) {
    logger.error('Error getting recent approval activity:', error);
    return [];
  }
};

module.exports = {
  getApprovals,
  getApprovalById,
  approveExpense,
  rejectExpense,
  escalateApproval,
  getPendingApprovals,
  bulkApprove,
  bulkReject,
  getApprovalStats
};