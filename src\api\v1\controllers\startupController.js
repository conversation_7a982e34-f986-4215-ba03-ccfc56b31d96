'use strict';

const startupFinanceService = require('../../../services/startupFinanceService');
const { CashPosition, RunwayAlert, ScenarioPlanning } = require('../../../models');
const { ApiError } = require('../../../utils/apiError');
const logger = require('../../../utils/logger');
const { Op } = require('sequelize');

/**
 * Get current cash position
 */
const getCashPosition = async (req, res, next) => {
  try {
    const companyId = req.user.companyId;
    
    if (!companyId) {
      throw ApiError.badRequest('User must be associated with a company');
    }
    
    const currentCash = await startupFinanceService.getCurrentCashPosition(companyId);
    
    // Get recent cash position history
    const history = await CashPosition.findAll({
      where: { companyId },
      order: [['date', 'DESC']],
      limit: 30
    });
    
    res.status(200).json({
      status: 'success',
      data: {
        currentBalance: currentCash,
        currency: 'USD',
        lastUpdated: new Date(),
        history: history.map(pos => ({
          date: pos.date,
          balance: pos.currentBalance,
          source: pos.source
        }))
      }
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Update cash position manually
 */
const updateCashPosition = async (req, res, next) => {
  try {
    const companyId = req.user.companyId;
    const { currentBalance, date, notes } = req.body;
    
    if (!companyId) {
      throw ApiError.badRequest('User must be associated with a company');
    }
    
    if (!currentBalance || currentBalance < 0) {
      throw ApiError.badRequest('Valid current balance is required');
    }
    
    const cashPosition = await CashPosition.create({
      companyId,
      currentBalance,
      date: date || new Date(),
      source: 'manual',
      notes
    });
    
    res.status(201).json({
      status: 'success',
      data: cashPosition
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Get burn rate calculation
 */
const getBurnRate = async (req, res, next) => {
  try {
    const companyId = req.user.companyId;
    const { month } = req.query;
    
    if (!companyId) {
      throw ApiError.badRequest('User must be associated with a company');
    }
    
    const targetMonth = month ? new Date(month) : new Date();
    const burnRate = await startupFinanceService.calculateMonthlyBurnRate(companyId, targetMonth);
    
    res.status(200).json({
      status: 'success',
      data: burnRate
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Get runway calculation
 */
const getRunway = async (req, res, next) => {
  try {
    const companyId = req.user.companyId;
    
    if (!companyId) {
      throw ApiError.badRequest('User must be associated with a company');
    }
    
    const runway = await startupFinanceService.calculateRunway(companyId);
    
    res.status(200).json({
      status: 'success',
      data: runway
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Get cash flow projections
 */
const getCashFlowProjections = async (req, res, next) => {
  try {
    const companyId = req.user.companyId;
    const { months = 6 } = req.query;
    
    if (!companyId) {
      throw ApiError.badRequest('User must be associated with a company');
    }
    
    const projections = await startupFinanceService.generateCashFlowProjections(
      companyId, 
      parseInt(months)
    );
    
    res.status(200).json({
      status: 'success',
      data: projections
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Get active runway alerts
 */
const getRunwayAlerts = async (req, res, next) => {
  try {
    const companyId = req.user.companyId;
    
    if (!companyId) {
      throw ApiError.badRequest('User must be associated with a company');
    }
    
    const alerts = await RunwayAlert.findAll({
      where: { 
        companyId, 
        isActive: true 
      },
      order: [['severity', 'DESC'], ['createdAt', 'DESC']]
    });
    
    res.status(200).json({
      status: 'success',
      data: alerts
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Dismiss a runway alert
 */
const dismissRunwayAlert = async (req, res, next) => {
  try {
    const { alertId } = req.params;
    const companyId = req.user.companyId;
    
    const alert = await RunwayAlert.findOne({
      where: { 
        id: alertId, 
        companyId,
        isActive: true 
      }
    });
    
    if (!alert) {
      throw ApiError.notFound('Alert not found');
    }
    
    alert.isActive = false;
    alert.dismissedAt = new Date();
    alert.dismissedBy = req.user.id;
    await alert.save();
    
    res.status(200).json({
      status: 'success',
      message: 'Alert dismissed successfully'
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Get startup dashboard overview
 */
const getDashboardOverview = async (req, res, next) => {
  try {
    const companyId = req.user.companyId;
    
    if (!companyId) {
      throw ApiError.badRequest('User must be associated with a company');
    }
    
    // Get all key metrics
    const [runway, healthScore, alerts] = await Promise.all([
      startupFinanceService.calculateRunway(companyId),
      startupFinanceService.calculateHealthScore(companyId),
      RunwayAlert.findAll({
        where: { companyId, isActive: true },
        order: [['severity', 'DESC']]
      })
    ]);
    
    // Get expense breakdown for current month
    const startOfMonth = new Date();
    startOfMonth.setDate(1);
    startOfMonth.setHours(0, 0, 0, 0);
    
    const endOfMonth = new Date();
    endOfMonth.setMonth(endOfMonth.getMonth() + 1);
    endOfMonth.setDate(0);
    endOfMonth.setHours(23, 59, 59, 999);
    
    const { Expense, Category } = require('../../../models');
    const expenseBreakdown = await Expense.findAll({
      where: {
        companyId,
        expenseDate: {
          [Op.between]: [startOfMonth, endOfMonth]
        },
        status: 'approved'
      },
      include: [{
        model: Category,
        as: 'category',
        attributes: ['name']
      }],
      attributes: ['categoryId', 'amountInBaseCurrency']
    });
    
    // Group expenses by category
    const categoryTotals = {};
    expenseBreakdown.forEach(expense => {
      const categoryName = expense.category?.name || 'Uncategorized';
      categoryTotals[categoryName] = (categoryTotals[categoryName] || 0) + parseFloat(expense.amountInBaseCurrency);
    });
    
    res.status(200).json({
      status: 'success',
      data: {
        currentCash: runway.currentCash,
        monthlyBurnRate: runway.avgMonthlyBurn,
        runwayMonths: runway.runwayMonths,
        runwayDays: runway.runwayDays,
        healthScore: healthScore.healthScore,
        expenseCategories: categoryTotals,
        activeAlerts: alerts.length,
        alerts: alerts.slice(0, 5), // Top 5 alerts
        trends: {
          burnRateTrend: healthScore.factors.burnTrend > 15 ? 'improving' : 'worsening',
          runwayTrend: runway.runwayMonths > 6 ? 'stable' : 'declining',
          cashTrend: 'stable' // Can be enhanced with historical data
        },
        lastUpdated: new Date()
      }
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Get financial health score
 */
const getHealthScore = async (req, res, next) => {
  try {
    const companyId = req.user.companyId;
    
    if (!companyId) {
      throw ApiError.badRequest('User must be associated with a company');
    }
    
    const healthScore = await startupFinanceService.calculateHealthScore(companyId);
    
    res.status(200).json({
      status: 'success',
      data: healthScore
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Create a new scenario
 */
const createScenario = async (req, res, next) => {
  try {
    const companyId = req.user.companyId;
    const { scenarioName, scenarioType, parameters, description } = req.body;
    
    if (!companyId) {
      throw ApiError.badRequest('User must be associated with a company');
    }
    
    // Calculate scenario impact
    const impact = await startupFinanceService.calculateScenarioImpact(companyId, {
      scenarioType,
      parameters
    });
    
    // Create scenario record
    const scenario = await ScenarioPlanning.create({
      companyId,
      scenarioName,
      scenarioType,
      baseMonthlyBurn: impact.currentBurn,
      projectedMonthlyBurn: impact.newMonthlyBurn || impact.currentBurn,
      impactOnRunway: impact.runwayImpact,
      parameters,
      description,
      createdBy: req.user.id
    });
    
    res.status(201).json({
      status: 'success',
      data: {
        scenario,
        impact
      }
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Get all scenarios for a company
 */
const getScenarios = async (req, res, next) => {
  try {
    const companyId = req.user.companyId;
    
    if (!companyId) {
      throw ApiError.badRequest('User must be associated with a company');
    }
    
    const scenarios = await ScenarioPlanning.findAll({
      where: { companyId, isActive: true },
      include: [{
        model: require('../../../models').User,
        as: 'creator',
        attributes: ['firstName', 'lastName', 'email']
      }],
      order: [['createdAt', 'DESC']]
    });
    
    res.status(200).json({
      status: 'success',
      data: scenarios
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Calculate scenario impact
 */
const calculateScenarioImpact = async (req, res, next) => {
  try {
    const companyId = req.user.companyId;
    const { scenarioType, parameters } = req.body;
    
    if (!companyId) {
      throw ApiError.badRequest('User must be associated with a company');
    }
    
    const impact = await startupFinanceService.calculateScenarioImpact(companyId, {
      scenarioType,
      parameters
    });
    
    res.status(200).json({
      status: 'success',
      data: impact
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Delete a scenario
 */
const deleteScenario = async (req, res, next) => {
  try {
    const { scenarioId } = req.params;
    const companyId = req.user.companyId;
    
    const scenario = await ScenarioPlanning.findOne({
      where: { 
        id: scenarioId, 
        companyId 
      }
    });
    
    if (!scenario) {
      throw ApiError.notFound('Scenario not found');
    }
    
    scenario.isActive = false;
    await scenario.save();
    
    res.status(200).json({
      status: 'success',
      message: 'Scenario deleted successfully'
    });
  } catch (error) {
    next(error);
  }
};

module.exports = {
  getCashPosition,
  updateCashPosition,
  getBurnRate,
  getRunway,
  getCashFlowProjections,
  getRunwayAlerts,
  dismissRunwayAlert,
  getDashboardOverview,
  getHealthScore,
  createScenario,
  getScenarios,
  calculateScenarioImpact,
  deleteScenario
};