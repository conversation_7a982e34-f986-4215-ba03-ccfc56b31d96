'use strict';

module.exports = (sequelize, DataTypes) => {
  const CashPosition = sequelize.define('CashPosition', {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true
    },
    companyId: {
      type: DataTypes.UUID,
      allowNull: false,
      references: {
        model: 'Companies',
        key: 'id'
      }
    },
    currentBalance: {
      type: DataTypes.DECIMAL(15, 2),
      allowNull: false
    },
    date: {
      type: DataTypes.DATEONLY,
      allowNull: false
    },
    source: {
      type: DataTypes.ENUM('bank_sync', 'manual', 'calculated'),
      defaultValue: 'calculated'
    },
    currency: {
      type: DataTypes.STRING,
      defaultValue: 'USD'
    },
    notes: {
      type: DataTypes.TEXT
    }
  }, {
    timestamps: true,
    indexes: [
      { fields: ['companyId'] },
      { fields: ['date'] },
      { fields: ['companyId', 'date'], unique: true }
    ]
  });

  CashPosition.associate = (models) => {
    CashPosition.belongsTo(models.Company, {
      foreignKey: 'companyId',
      as: 'company'
    });
  };

  return CashPosition;
};