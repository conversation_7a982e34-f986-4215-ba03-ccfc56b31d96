'use strict';

module.exports = (sequelize, DataTypes) => {
  const ExchangeRate = sequelize.define('ExchangeRate', {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true
    },
    fromCurrency: {
      type: DataTypes.STRING(3),
      allowNull: false
    },
    toCurrency: {
      type: DataTypes.STRING(3),
      allowNull: false
    },
    rate: {
      type: DataTypes.DECIMAL(19, 6),
      allowNull: false
    },
    date: {
      type: DataTypes.DATEONLY,
      allowNull: false
    },
    source: {
      type: DataTypes.STRING(50),
      defaultValue: 'api'
    }
  }, {
    timestamps: true,
    indexes: [
      { fields: ['fromCurrency', 'toCurrency', 'date'], unique: true },
      { fields: ['date'] }
    ]
  });

  return ExchangeRate;
};