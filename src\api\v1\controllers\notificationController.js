'use strict';

const notificationService = require('../../../services/notificationService');
const { ApiError } = require('../../../utils/apiError');
const logger = require('../../../utils/logger');

/**
 * Get user notifications
 */
const getNotifications = async (req, res, next) => {
  try {
    const userId = req.user.id;
    const options = {
      page: parseInt(req.query.page) || 1,
      limit: parseInt(req.query.limit) || 20,
      type: req.query.type,
      isRead: req.query.isRead !== undefined ? req.query.isRead === 'true' : undefined,
      priority: req.query.priority
    };

    const result = await notificationService.getUserNotifications(userId, options);

    res.status(200).json({
      status: 'success',
      data: result
    });
  } catch (error) {
    logger.error('Error getting notifications:', error);
    next(error);
  }
};

/**
 * Mark notification as read
 */
const markAsRead = async (req, res, next) => {
  try {
    const { notificationId } = req.params;
    const userId = req.user.id;

    const notification = await notificationService.markAsRead(notificationId, userId);

    res.status(200).json({
      status: 'success',
      data: notification
    });
  } catch (error) {
    logger.error('Error marking notification as read:', error);
    next(error);
  }
};

/**
 * Mark all notifications as read
 */
const markAllAsRead = async (req, res, next) => {
  try {
    const userId = req.user.id;
    const updatedCount = await notificationService.markAllAsRead(userId);

    res.status(200).json({
      status: 'success',
      data: {
        updatedCount
      }
    });
  } catch (error) {
    logger.error('Error marking all notifications as read:', error);
    next(error);
  }
};

/**
 * Delete notification
 */
const deleteNotification = async (req, res, next) => {
  try {
    const { notificationId } = req.params;
    const userId = req.user.id;

    await notificationService.deleteNotification(notificationId, userId);

    res.status(200).json({
      status: 'success',
      message: 'Notification deleted successfully'
    });
  } catch (error) {
    logger.error('Error deleting notification:', error);
    next(error);
  }
};

/**
 * Get notification settings
 */
const getSettings = async (req, res, next) => {
  try {
    // For now, return default settings
    // In a real implementation, you would store user preferences
    const settings = {
      email: {
        expenseApproved: true,
        expenseRejected: true,
        budgetAlert: true,
        runwayWarning: true,
        systemUpdate: false
      },
      push: {
        expenseApproved: true,
        expenseRejected: true,
        budgetAlert: true,
        runwayWarning: true,
        systemUpdate: false
      },
      frequency: 'immediate' // immediate, daily, weekly
    };

    res.status(200).json({
      status: 'success',
      data: settings
    });
  } catch (error) {
    logger.error('Error getting notification settings:', error);
    next(error);
  }
};

/**
 * Update notification settings
 */
const updateSettings = async (req, res, next) => {
  try {
    const { email, push, frequency } = req.body;

    // In a real implementation, you would save these to a UserNotificationSettings model
    const updatedSettings = {
      email: email || {},
      push: push || {},
      frequency: frequency || 'immediate'
    };

    res.status(200).json({
      status: 'success',
      data: updatedSettings,
      message: 'Notification settings updated successfully'
    });
  } catch (error) {
    logger.error('Error updating notification settings:', error);
    next(error);
  }
};

/**
 * Get notification statistics
 */
const getStats = async (req, res, next) => {
  try {
    const userId = req.user.id;
    const stats = await notificationService.getNotificationStats(userId);

    res.status(200).json({
      status: 'success',
      data: stats
    });
  } catch (error) {
    logger.error('Error getting notification stats:', error);
    next(error);
  }
};

module.exports = {
  getNotifications,
  markAsRead,
  markAllAsRead,
  deleteNotification,
  getSettings,
  updateSettings,
  getStats
};