# Database Configuration
DB_HOST=finance-db.cluster-c5oiceume4mj.ap-south-1.rds.amazonaws.com
DB_PORT=5432
DB_NAME=finance-db
DB_USER=postgres
DB_PASSWORD=vishwa555

# Redis Configuration
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-here
JWT_REFRESH_SECRET=your-super-secret-refresh-key-here

# API Keys
EXCHANGE_RATE_API_KEY=your-exchange-rate-api-key
COHERE_API_KEY=your-cohere-api-key-here

# AWS Configuration
AWS_ACCESS_KEY_ID=your-aws-access-key-id
AWS_SECRET_ACCESS_KEY=your-aws-secret-access-key
AWS_REGION=us-east-1
AWS_S3_BUCKET=finance-manager-uploads

# Application Configuration
NODE_ENV=development
PORT=3000
FRONTEND_URL=http://localhost:3000

# Email Configuration (for notifications)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password

# Logging
LOG_LEVEL=info