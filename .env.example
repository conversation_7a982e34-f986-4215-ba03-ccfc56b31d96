# Database Configuration
DB_HOST=finance-db.cluster-c5oiceume4mj.ap-south-1.rds.amazonaws.com
DB_PORT=5432
DB_NAME=finance-db
DB_USER=postgres
DB_PASSWORD=vishwa555

# Redis Configuration
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-here
JWT_REFRESH_SECRET=your-super-secret-refresh-key-here

# API Keys
EXCHANGE_RATE_API_KEY=5e66cb38c69609aa9e3ce33c
COHERE_API_KEY=aKPbm8ElEl3qSA8FHQ8sHPzObmo1kcH0S4oeTOWK

# AWS Configuration
AWS_ACCESS_KEY_ID=********************
AWS_SECRET_ACCESS_KEY=w12V/FepnHajuuax4WQuvb/5G5XEkzCX4l9Bpuxz
AWS_REGION=eu-north-1
AWS_S3_BUCKET=finance-manager-uploads

# Application Configuration
NODE_ENV=development
PORT=3000
FRONTEND_URL=http://localhost:3000

# Email Configuration (for notifications)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password

# Logging
LOG_LEVEL=info