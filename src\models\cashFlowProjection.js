'use strict';

module.exports = (sequelize, DataTypes) => {
  const CashFlowProjection = sequelize.define('CashFlowProjection', {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true
    },
    companyId: {
      type: DataTypes.UUID,
      allowNull: false,
      references: {
        model: 'Companies',
        key: 'id'
      }
    },
    projectionDate: {
      type: DataTypes.DATEONLY,
      allowNull: false
    },
    projectedBalance: {
      type: DataTypes.DECIMAL(15, 2),
      allowNull: false
    },
    projectedBurn: {
      type: DataTypes.DECIMAL(15, 2),
      allowNull: false
    },
    scenarioType: {
      type: DataTypes.ENUM('current', 'optimistic', 'pessimistic'),
      defaultValue: 'current'
    },
    confidenceScore: {
      type: DataTypes.FLOAT,
      defaultValue: 0.8,
      validate: {
        min: 0,
        max: 1
      }
    },
    currency: {
      type: DataTypes.STRING,
      defaultValue: 'USD'
    },
    assumptions: {
      type: DataTypes.JSONB,
      defaultValue: {}
    }
  }, {
    timestamps: true,
    indexes: [
      { fields: ['companyId'] },
      { fields: ['projectionDate'] },
      { fields: ['scenarioType'] }
    ]
  });

  CashFlowProjection.associate = (models) => {
    CashFlowProjection.belongsTo(models.Company, {
      foreignKey: 'companyId',
      as: 'company'
    });
  };

  return CashFlowProjection;
};