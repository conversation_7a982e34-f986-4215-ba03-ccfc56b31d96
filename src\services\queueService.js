'use strict';

const Bull = require('bull');
const logger = require('../utils/logger');
const startupTaskService = require('./startupTaskService');

// Create Redis connection config
const redisConfig = {
  redis: {
    host: process.env.REDIS_HOST || 'localhost',
    port: parseInt(process.env.REDIS_PORT || '6379'),
    password: process.env.REDIS_PASSWORD || undefined
  }
};

// Define queues
const receiptProcessingQueue = new Bull('receipt-processing', redisConfig);
const bankSyncQueue = new Bull('bank-sync', redisConfig);
const reportGenerationQueue = new Bull('report-generation', redisConfig);
const notificationQueue = new Bull('notifications', redisConfig);

// NEW: Startup finance queues
const startupFinanceQueue = new Bull('startup-finance', redisConfig);

// Set up queue event handlers
const setupQueueEvents = (queue) => {
  queue.on('error', (error) => {
    logger.error(`Queue ${queue.name} error:`, error);
  });

  queue.on('failed', (job, error) => {
    logger.error(`Job ${job.id} in ${queue.name} queue failed:`, error);
  });

  queue.on('completed', (job) => {
    logger.info(`Job ${job.id} in ${queue.name} queue completed successfully`);
  });
};

// Initialize event handlers for all queues
setupQueueEvents(receiptProcessingQueue);
setupQueueEvents(bankSyncQueue);
setupQueueEvents(reportGenerationQueue);
setupQueueEvents(notificationQueue);
setupQueueEvents(startupFinanceQueue);

/**
 * Process OCR for receipt images
 */
receiptProcessingQueue.process(async (job) => {
  logger.info(`Processing receipt OCR for job ${job.id}`);
  // OCR processing logic would be implemented here
  // This would integrate with Google Gemini API
  return { status: 'success', receiptId: job.data.receiptId };
});

/**
 * Sync bank transactions
 */
bankSyncQueue.process(async (job) => {
  logger.info(`Syncing bank transactions for job ${job.id}`);
  // Bank sync logic would be implemented here
  // This would integrate with Plaid or other banking APIs
  return { status: 'success', accountId: job.data.accountId };
});

/**
 * Generate financial reports
 */
reportGenerationQueue.process(async (job) => {
  logger.info(`Generating report for job ${job.id}`);
  // Report generation logic would be implemented here
  return { status: 'success', reportId: job.data.reportId };
});

/**
 * Send notifications
 */
notificationQueue.process(async (job) => {
  logger.info(`Sending notification for job ${job.id}`);
  // Notification logic would be implemented here
  return { status: 'success', recipientId: job.data.recipientId };
});

/**
 * NEW: Process startup finance tasks
 */
startupFinanceQueue.process('update-cash-positions', async (job) => {
  logger.info(`Updating cash positions for job ${job.id}`);
  return await startupTaskService.updateAllCashPositions();
});

startupFinanceQueue.process('calculate-burn-rates', async (job) => {
  logger.info(`Calculating burn rates for job ${job.id}`);
  return await startupTaskService.calculateAllBurnRates();
});

startupFinanceQueue.process('generate-projections', async (job) => {
  logger.info(`Generating runway projections for job ${job.id}`);
  return await startupTaskService.generateAllRunwayProjections();
});

startupFinanceQueue.process('check-financial-health', async (job) => {
  logger.info(`Checking financial health for job ${job.id}`);
  return await startupTaskService.checkAllFinancialHealth();
});

startupFinanceQueue.process('daily-tasks', async (job) => {
  logger.info(`Running daily startup finance tasks for job ${job.id}`);
  return await startupTaskService.runDailyTasks();
});

startupFinanceQueue.process('weekly-tasks', async (job) => {
  logger.info(`Running weekly startup finance tasks for job ${job.id}`);
  return await startupTaskService.runWeeklyTasks();
});

// Schedule recurring jobs
const scheduleStartupFinanceTasks = () => {
  // Daily tasks at 6 AM
  startupFinanceQueue.add('daily-tasks', {}, {
    repeat: { cron: '0 6 * * *' },
    removeOnComplete: 5,
    removeOnFail: 3
  });
  
  // Weekly tasks on Sundays at 7 AM
  startupFinanceQueue.add('weekly-tasks', {}, {
    repeat: { cron: '0 7 * * 0' },
    removeOnComplete: 5,
    removeOnFail: 3
  });
  
  logger.info('Startup finance recurring tasks scheduled');
};

// Initialize recurring tasks
scheduleStartupFinanceTasks();

module.exports = {
  queues: {
    receiptProcessingQueue,
    bankSyncQueue,
    reportGenerationQueue,
    notificationQueue,
    startupFinanceQueue
  },
  
  /**
   * Add a job to process a receipt with OCR
   */
  processReceipt: async (receiptData) => {
    return await receiptProcessingQueue.add(receiptData, {
      attempts: 3,
      backoff: {
        type: 'exponential',
        delay: 5000
      }
    });
  },
  
  /**
   * Add a job to sync bank transactions
   */
  syncBankAccount: async (accountData) => {
    return await bankSyncQueue.add(accountData, {
      attempts: 3,
      backoff: {
        type: 'exponential',
        delay: 10000
      }
    });
  },
  
  /**
   * Add a job to generate a financial report
   */
  generateReport: async (reportData) => {
    return await reportGenerationQueue.add(reportData, {
      attempts: 2,
      backoff: {
        type: 'fixed',
        delay: 5000
      }
    });
  },
  
  /**
   * Add a job to send a notification
   */
  sendNotification: async (notificationData) => {
    return await notificationQueue.add(notificationData, {
      attempts: 3,
      backoff: {
        type: 'exponential',
        delay: 2000
      }
    });
  },
  
  // NEW: Startup finance job methods
  /**
   * Add a job to update cash positions
   */
  updateCashPositions: async () => {
    return await startupFinanceQueue.add('update-cash-positions', {}, {
      attempts: 2,
      backoff: {
        type: 'fixed',
        delay: 5000
      }
    });
  },
  
  /**
   * Add a job to calculate burn rates
   */
  calculateBurnRates: async () => {
    return await startupFinanceQueue.add('calculate-burn-rates', {}, {
      attempts: 2,
      backoff: {
        type: 'fixed',
        delay: 5000
      }
    });
  },
  
  /**
   * Add a job to generate runway projections
   */
  generateRunwayProjections: async () => {
    return await startupFinanceQueue.add('generate-projections', {}, {
      attempts: 2,
      backoff: {
        type: 'fixed',
        delay: 5000
      }
    });
  },
  
  /**
   * Add a job to check financial health
   */
  checkFinancialHealth: async () => {
    return await startupFinanceQueue.add('check-financial-health', {}, {
      attempts: 2,
      backoff: {
        type: 'fixed',
        delay: 5000
      }
    });
  }
};