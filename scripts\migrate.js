const { runMigrations } = require('../config/database');
const logger = require('../utils/logger');

async function migrate() {
    try {
        logger.info('Starting database migration...');
        await runMigrations();
        logger.info('Database migration completed successfully');
        process.exit(0);
    } catch (error) {
        logger.error('Migration failed:', error);
        process.exit(1);
    }
}

migrate();