'use strict';

module.exports = (sequelize, DataTypes) => {
  const TwoFactorAuth = sequelize.define('TwoFactorAuth', {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true
    },
    userId: {
      type: DataTypes.UUID,
      allowNull: false,
      unique: true,
      references: {
        model: 'Users',
        key: 'id'
      }
    },
    isEnabled: {
      type: DataTypes.BOOLEAN,
      defaultValue: false
    },
    secret: {
      type: DataTypes.STRING
    },
    backupCodes: {
      type: DataTypes.ARRAY(DataTypes.STRING),
      defaultValue: []
    },
    lastUsed: {
      type: DataTypes.DATE
    },
    failedAttempts: {
      type: DataTypes.INTEGER,
      defaultValue: 0
    },
    lockedUntil: {
      type: DataTypes.DATE
    }
  }, {
    timestamps: true,
    indexes: [
      { fields: ['userId'] },
      { fields: ['isEnabled'] }
    ]
  });

  TwoFactorAuth.associate = (models) => {
    TwoFactorAuth.belongsTo(models.User, {
      foreignKey: 'userId',
      as: 'user'
    });
  };

  return TwoFactorAuth;
};