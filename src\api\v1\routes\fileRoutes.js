'use strict';

const express = require('express');
const { authenticate } = require('../../../middleware/auth');
const upload = require('../../../middleware/upload');
const fileController = require('../controllers/fileController');

const router = express.Router();

// All routes require authentication
router.use(authenticate);

/**
 * @swagger
 * /api/v1/files/upload:
 *   post:
 *     summary: Upload a file
 *     tags: [Files]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         multipart/form-data:
 *           schema:
 *             type: object
 *             required:
 *               - file
 *             properties:
 *               file:
 *                 type: string
 *                 format: binary
 *               entityType:
 *                 type: string
 *                 description: Type of entity this file belongs to
 *               entityId:
 *                 type: string
 *                 format: uuid
 *                 description: ID of the entity this file belongs to
 *     responses:
 *       201:
 *         description: File uploaded successfully
 */
router.post('/upload', upload.single('file'), fileController.uploadFile);

/**
 * @swagger
 * /api/v1/files/{fileId}:
 *   get:
 *     summary: Get file by ID
 *     tags: [Files]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: fileId
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *     responses:
 *       200:
 *         description: File retrieved successfully
 *         content:
 *           application/octet-stream:
 *             schema:
 *               type: string
 *               format: binary
 */
router.get('/:fileId', fileController.getFile);

/**
 * @swagger
 * /api/v1/files/{fileId}:
 *   delete:
 *     summary: Delete file
 *     tags: [Files]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: fileId
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *     responses:
 *       200:
 *         description: File deleted successfully
 */
router.delete('/:fileId', fileController.deleteFile);

/**
 * @swagger
 * /api/v1/files/{fileId}/thumbnail:
 *   get:
 *     summary: Get file thumbnail
 *     tags: [Files]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: fileId
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *     responses:
 *       200:
 *         description: Thumbnail retrieved successfully
 */
router.get('/:fileId/thumbnail', fileController.getThumbnail);

/**
 * @swagger
 * /api/v1/files/bulk-upload:
 *   post:
 *     summary: Upload multiple files
 *     tags: [Files]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         multipart/form-data:
 *           schema:
 *             type: object
 *             required:
 *               - files
 *             properties:
 *               files:
 *                 type: array
 *                 items:
 *                   type: string
 *                   format: binary
 *               entityType:
 *                 type: string
 *               entityId:
 *                 type: string
 *                 format: uuid
 *     responses:
 *       201:
 *         description: Files uploaded successfully
 */
router.post('/bulk-upload', upload.array('files', 10), fileController.bulkUpload);

/**
 * @swagger
 * /api/v1/files/{fileId}/metadata:
 *   get:
 *     summary: Get file metadata
 *     tags: [Files]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: fileId
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *     responses:
 *       200:
 *         description: File metadata retrieved successfully
 */
router.get('/:fileId/metadata', fileController.getFileMetadata);

module.exports = router;