'use strict';

const { Op } = require('sequelize');
const { 
  CashPosition, 
  BurnRateCalculation, 
  CashFlowProjection, 
  RunwayAlert, 
  ScenarioPlanning,
  Expense,
  BankAccount,
  Transaction,
  Company
} = require('../models');
const logger = require('../utils/logger');
const { ApiError } = require('../utils/apiError');

/**
 * Service for startup financial calculations and management
 */
class StartupFinanceService {
  
  /**
   * Calculate monthly burn rate for a company
   * @param {string} companyId - Company ID
   * @param {Date} month - Month to calculate for
   * @returns {Promise<Object>} Burn rate calculation
   */
  async calculateMonthlyBurnRate(companyId, month = new Date()) {
    try {
      const startOfMonth = new Date(month.getFullYear(), month.getMonth(), 1);
      const endOfMonth = new Date(month.getFullYear(), month.getMonth() + 1, 0);
      
      // Get total approved expenses for the month
      const totalExpenses = await Expense.sum('amountInBaseCurrency', {
        where: {
          companyId,
          expenseDate: {
            [Op.between]: [startOfMonth, endOfMonth]
          },
          status: 'approved'
        }
      }) || 0;
      
      // For now, assume revenue is 0 (can be enhanced later)
      const totalRevenue = 0;
      const netBurn = totalExpenses - totalRevenue;
      
      // Calculate runway based on current cash position
      const currentCash = await this.getCurrentCashPosition(companyId);
      const runwayMonths = netBurn > 0 ? Math.floor(currentCash / netBurn) : 999;
      
      // Save calculation
      const calculation = await BurnRateCalculation.upsert({
        companyId,
        month: startOfMonth,
        totalExpenses,
        totalRevenue,
        netBurn,
        runwayMonths,
        isAutomated: true
      });
      
      return {
        month: startOfMonth,
        totalExpenses,
        totalRevenue,
        netBurn,
        runwayMonths,
        calculation: calculation[0]
      };
    } catch (error) {
      logger.error('Error calculating monthly burn rate:', error);
      throw new ApiError('Failed to calculate burn rate', 500);
    }
  }
  
  /**
   * Get current cash position for a company
   * @param {string} companyId - Company ID
   * @returns {Promise<number>} Current cash balance
   */
  async getCurrentCashPosition(companyId) {
    try {
      // Try to get the latest cash position record
      const latestPosition = await CashPosition.findOne({
        where: { companyId },
        order: [['date', 'DESC']]
      });
      
      if (latestPosition) {
        return parseFloat(latestPosition.currentBalance);
      }
      
      // If no cash position record, try to calculate from bank accounts
      const bankAccounts = await BankAccount.findAll({
        where: { companyId, isActive: true }
      });
      
      const totalBalance = bankAccounts.reduce((sum, account) => {
        return sum + parseFloat(account.balance || 0);
      }, 0);
      
      // Create initial cash position record
      if (totalBalance > 0) {
        await CashPosition.create({
          companyId,
          currentBalance: totalBalance,
          date: new Date(),
          source: 'bank_sync'
        });
      }
      
      return totalBalance;
    } catch (error) {
      logger.error('Error getting current cash position:', error);
      throw new ApiError('Failed to get cash position', 500);
    }
  }
  
  /**
   * Calculate runway for a company
   * @param {string} companyId - Company ID
   * @returns {Promise<Object>} Runway calculation
   */
  async calculateRunway(companyId) {
    try {
      const currentCash = await this.getCurrentCashPosition(companyId);
      
      // Get average burn rate over last 3 months
      const threeMonthsAgo = new Date();
      threeMonthsAgo.setMonth(threeMonthsAgo.getMonth() - 3);
      
      const burnRates = await BurnRateCalculation.findAll({
        where: {
          companyId,
          month: {
            [Op.gte]: threeMonthsAgo
          }
        },
        order: [['month', 'DESC']],
        limit: 3
      });
      
      let avgMonthlyBurn = 0;
      if (burnRates.length > 0) {
        const totalBurn = burnRates.reduce((sum, rate) => sum + parseFloat(rate.netBurn), 0);
        avgMonthlyBurn = totalBurn / burnRates.length;
      } else {
        // Calculate current month burn if no historical data
        const currentMonthBurn = await this.calculateMonthlyBurnRate(companyId);
        avgMonthlyBurn = currentMonthBurn.netBurn;
      }
      
      const runwayMonths = avgMonthlyBurn > 0 ? currentCash / avgMonthlyBurn : 999;
      
      return {
        currentCash,
        avgMonthlyBurn,
        runwayMonths: Math.max(0, Math.floor(runwayMonths)),
        runwayDays: Math.max(0, Math.floor(runwayMonths * 30)),
        lastUpdated: new Date()
      };
    } catch (error) {
      logger.error('Error calculating runway:', error);
      throw new ApiError('Failed to calculate runway', 500);
    }
  }
  
  /**
   * Generate cash flow projections
   * @param {string} companyId - Company ID
   * @param {number} months - Number of months to project
   * @returns {Promise<Array>} Cash flow projections
   */
  async generateCashFlowProjections(companyId, months = 6) {
    try {
      const runway = await this.calculateRunway(companyId);
      const projections = [];
      
      for (let i = 1; i <= months; i++) {
        const projectionDate = new Date();
        projectionDate.setMonth(projectionDate.getMonth() + i);
        
        // Current scenario - linear burn
        const currentBalance = Math.max(0, runway.currentCash - (runway.avgMonthlyBurn * i));
        
        // Optimistic scenario - 20% less burn
        const optimisticBalance = Math.max(0, runway.currentCash - (runway.avgMonthlyBurn * 0.8 * i));
        
        // Pessimistic scenario - 30% more burn
        const pessimisticBalance = Math.max(0, runway.currentCash - (runway.avgMonthlyBurn * 1.3 * i));
        
        const scenarios = [
          {
            scenarioType: 'current',
            projectedBalance: currentBalance,
            projectedBurn: runway.avgMonthlyBurn,
            confidenceScore: 0.8
          },
          {
            scenarioType: 'optimistic',
            projectedBalance: optimisticBalance,
            projectedBurn: runway.avgMonthlyBurn * 0.8,
            confidenceScore: 0.6
          },
          {
            scenarioType: 'pessimistic',
            projectedBalance: pessimisticBalance,
            projectedBurn: runway.avgMonthlyBurn * 1.3,
            confidenceScore: 0.7
          }
        ];
        
        // Save projections to database
        for (const scenario of scenarios) {
          await CashFlowProjection.upsert({
            companyId,
            projectionDate,
            ...scenario
          });
        }
        
        projections.push({
          month: i,
          date: projectionDate,
          scenarios
        });
      }
      
      return projections;
    } catch (error) {
      logger.error('Error generating cash flow projections:', error);
      throw new ApiError('Failed to generate projections', 500);
    }
  }
  
  /**
   * Check and create runway alerts
   * @param {string} companyId - Company ID
   * @returns {Promise<Array>} Created alerts
   */
  async checkRunwayAlerts(companyId) {
    try {
      const runway = await this.calculateRunway(companyId);
      const alerts = [];
      
      // Deactivate old alerts
      await RunwayAlert.update(
        { isActive: false },
        { where: { companyId, isActive: true } }
      );
      
      // Critical runway (< 3 months)
      if (runway.runwayMonths < 3) {
        const alert = await RunwayAlert.create({
          companyId,
          alertType: 'critical_runway',
          message: `Critical: Only ${runway.runwayMonths} months of runway remaining`,
          thresholdValue: 3,
          currentValue: runway.runwayMonths,
          severity: 'critical'
        });
        alerts.push(alert);
      }
      // Warning runway (< 6 months)
      else if (runway.runwayMonths < 6) {
        const alert = await RunwayAlert.create({
          companyId,
          alertType: 'runway_warning',
          message: `Warning: ${runway.runwayMonths} months runway - consider fundraising`,
          thresholdValue: 6,
          currentValue: runway.runwayMonths,
          severity: 'high'
        });
        alerts.push(alert);
      }
      
      // Cash low alert (< $50k)
      if (runway.currentCash < 50000) {
        const alert = await RunwayAlert.create({
          companyId,
          alertType: 'cash_low',
          message: `Low cash balance: $${runway.currentCash.toLocaleString()}`,
          thresholdValue: 50000,
          currentValue: runway.currentCash,
          severity: 'high'
        });
        alerts.push(alert);
      }
      
      return alerts;
    } catch (error) {
      logger.error('Error checking runway alerts:', error);
      throw new ApiError('Failed to check alerts', 500);
    }
  }
  
  /**
   * Calculate startup financial health score
   * @param {string} companyId - Company ID
   * @returns {Promise<Object>} Health score and breakdown
   */
  async calculateHealthScore(companyId) {
    try {
      const runway = await this.calculateRunway(companyId);
      let score = 0;
      const factors = {};
      
      // Runway score (40% weight)
      if (runway.runwayMonths >= 12) {
        factors.runway = 40;
      } else if (runway.runwayMonths >= 6) {
        factors.runway = 30;
      } else if (runway.runwayMonths >= 3) {
        factors.runway = 20;
      } else {
        factors.runway = 10;
      }
      
      // Cash position score (30% weight)
      if (runway.currentCash >= 500000) {
        factors.cashPosition = 30;
      } else if (runway.currentCash >= 250000) {
        factors.cashPosition = 25;
      } else if (runway.currentCash >= 100000) {
        factors.cashPosition = 20;
      } else if (runway.currentCash >= 50000) {
        factors.cashPosition = 15;
      } else {
        factors.cashPosition = 10;
      }
      
      // Burn rate trend (20% weight)
      const recentBurns = await BurnRateCalculation.findAll({
        where: { companyId },
        order: [['month', 'DESC']],
        limit: 3
      });
      
      if (recentBurns.length >= 2) {
        const latestBurn = parseFloat(recentBurns[0].netBurn);
        const previousBurn = parseFloat(recentBurns[1].netBurn);
        
        if (latestBurn < previousBurn) {
          factors.burnTrend = 20; // Improving
        } else if (latestBurn === previousBurn) {
          factors.burnTrend = 15; // Stable
        } else {
          factors.burnTrend = 10; // Worsening
        }
      } else {
        factors.burnTrend = 15; // Neutral for new companies
      }
      
      // Alert severity (10% weight)
      const activeAlerts = await RunwayAlert.count({
        where: { companyId, isActive: true }
      });
      
      if (activeAlerts === 0) {
        factors.alerts = 10;
      } else if (activeAlerts <= 2) {
        factors.alerts = 7;
      } else {
        factors.alerts = 5;
      }
      
      score = Object.values(factors).reduce((sum, value) => sum + value, 0);
      
      return {
        healthScore: Math.min(100, score),
        factors,
        runway: runway.runwayMonths,
        cashPosition: runway.currentCash,
        monthlyBurn: runway.avgMonthlyBurn,
        activeAlerts,
        lastCalculated: new Date()
      };
    } catch (error) {
      logger.error('Error calculating health score:', error);
      throw new ApiError('Failed to calculate health score', 500);
    }
  }
  
  /**
   * Calculate scenario impact
   * @param {string} companyId - Company ID
   * @param {Object} scenarioData - Scenario parameters
   * @returns {Promise<Object>} Scenario impact calculation
   */
  async calculateScenarioImpact(companyId, scenarioData) {
    try {
      const currentRunway = await this.calculateRunway(companyId);
      const { scenarioType, parameters } = scenarioData;
      
      let impactCalculation = {};
      
      switch (scenarioType) {
        case 'hiring':
          impactCalculation = await this._calculateHiringScenario(currentRunway, parameters);
          break;
        case 'revenue_growth':
          impactCalculation = await this._calculateRevenueGrowthScenario(currentRunway, parameters);
          break;
        case 'cost_reduction':
          impactCalculation = await this._calculateCostReductionScenario(currentRunway, parameters);
          break;
        case 'fundraising':
          impactCalculation = await this._calculateFundraisingScenario(currentRunway, parameters);
          break;
        default:
          throw new ApiError('Unsupported scenario type', 400);
      }
      
      return {
        scenarioType,
        parameters,
        currentRunway: currentRunway.runwayMonths,
        currentBurn: currentRunway.avgMonthlyBurn,
        ...impactCalculation
      };
    } catch (error) {
      logger.error('Error calculating scenario impact:', error);
      throw error instanceof ApiError ? error : new ApiError('Failed to calculate scenario', 500);
    }
  }
  
  /**
   * Calculate hiring scenario impact
   */
  async _calculateHiringScenario(currentRunway, parameters) {
    const { newHires, averageSalary, startDate } = parameters;
    const additionalMonthlyBurn = (newHires * averageSalary) / 12;
    const newMonthlyBurn = currentRunway.avgMonthlyBurn + additionalMonthlyBurn;
    const newRunwayMonths = Math.floor(currentRunway.currentCash / newMonthlyBurn);
    
    return {
      additionalMonthlyBurn,
      newMonthlyBurn,
      newRunwayMonths,
      runwayImpact: newRunwayMonths - currentRunway.runwayMonths,
      description: `Hiring ${newHires} employees at $${averageSalary.toLocaleString()} average salary`
    };
  }
  
  /**
   * Calculate revenue growth scenario impact
   */
  async _calculateRevenueGrowthScenario(currentRunway, parameters) {
    const { monthlyRevenueIncrease } = parameters;
    const newMonthlyBurn = Math.max(0, currentRunway.avgMonthlyBurn - monthlyRevenueIncrease);
    const newRunwayMonths = newMonthlyBurn > 0 ? Math.floor(currentRunway.currentCash / newMonthlyBurn) : 999;
    
    return {
      monthlyRevenueIncrease,
      newMonthlyBurn,
      newRunwayMonths,
      runwayImpact: newRunwayMonths - currentRunway.runwayMonths,
      description: `Adding $${monthlyRevenueIncrease.toLocaleString()} monthly revenue`
    };
  }
  
  /**
   * Calculate cost reduction scenario impact
   */
  async _calculateCostReductionScenario(currentRunway, parameters) {
    const { monthlySavings } = parameters;
    const newMonthlyBurn = Math.max(0, currentRunway.avgMonthlyBurn - monthlySavings);
    const newRunwayMonths = newMonthlyBurn > 0 ? Math.floor(currentRunway.currentCash / newMonthlyBurn) : 999;
    
    return {
      monthlySavings,
      newMonthlyBurn,
      newRunwayMonths,
      runwayImpact: newRunwayMonths - currentRunway.runwayMonths,
      description: `Reducing costs by $${monthlySavings.toLocaleString()} per month`
    };
  }
  
  /**
   * Calculate fundraising scenario impact
   */
  async _calculateFundraisingScenario(currentRunway, parameters) {
    const { fundraiseAmount } = parameters;
    const newCashPosition = currentRunway.currentCash + fundraiseAmount;
    const newRunwayMonths = Math.floor(newCashPosition / currentRunway.avgMonthlyBurn);
    
    return {
      fundraiseAmount,
      newCashPosition,
      newRunwayMonths,
      runwayImpact: newRunwayMonths - currentRunway.runwayMonths,
      description: `Raising $${fundraiseAmount.toLocaleString()} in funding`
    };
  }
}

module.exports = new StartupFinanceService();