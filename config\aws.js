const AWS = require('aws-sdk');
const logger = require('../utils/logger');

// Configure AWS
AWS.config.update({
    accessKeyId: process.env.AWS_ACCESS_KEY_ID,
    secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY,
    region: process.env.AWS_REGION || 'us-east-1'
});

const s3 = new AWS.S3();
const textract = new AWS.Textract();

// S3 Configuration
const S3_BUCKET = process.env.AWS_S3_BUCKET || 'finance-manager-uploads';
const S3_REGION = process.env.AWS_REGION || 'us-east-1';

class AWSService {
    constructor() {
        this.s3 = s3;
        this.textract = textract;
        this.bucket = S3_BUCKET;
    }

    // Upload file to S3
    async uploadFile(buffer, key, contentType) {
        try {
            const params = {
                Bucket: this.bucket,
                Key: key,
                Body: buffer,
                ContentType: contentType,
                ServerSideEncryption: 'AES256'
            };

            const result = await this.s3.upload(params).promise();
            logger.info(`File uploaded to S3: ${key}`);
            return result;
        } catch (error) {
            logger.error('S3 upload failed:', error);
            throw new Error('File upload failed');
        }
    }

    // Get file from S3
    async getFile(key) {
        try {
            const params = {
                Bucket: this.bucket,
                Key: key
            };

            const result = await this.s3.getObject(params).promise();
            return result.Body;
        } catch (error) {
            logger.error('S3 download failed:', error);
            throw new Error('File download failed');
        }
    }

    // Delete file from S3
    async deleteFile(key) {
        try {
            const params = {
                Bucket: this.bucket,
                Key: key
            };

            await this.s3.deleteObject(params).promise();
            logger.info(`File deleted from S3: ${key}`);
        } catch (error) {
            logger.error('S3 delete failed:', error);
            throw new Error('File deletion failed');
        }
    }

    // Extract text from PDF using Textract
    async extractTextFromPDF(s3Key) {
        try {
            const params = {
                Document: {
                    S3Object: {
                        Bucket: this.bucket,
                        Name: s3Key
                    }
                },
                FeatureTypes: ['TABLES', 'FORMS']
            };

            const result = await this.textract.analyzeDocument(params).promise();
            logger.info(`Textract analysis completed for: ${s3Key}`);
            return result;
        } catch (error) {
            logger.error('Textract analysis failed:', error);
            throw new Error('PDF text extraction failed');
        }
    }

    // Generate presigned URL for file access
    async generatePresignedUrl(key, expiresIn = 3600) {
        try {
            const params = {
                Bucket: this.bucket,
                Key: key,
                Expires: expiresIn
            };

            const url = await this.s3.getSignedUrlPromise('getObject', params);
            return url;
        } catch (error) {
            logger.error('Presigned URL generation failed:', error);
            throw new Error('URL generation failed');
        }
    }
}

module.exports = new AWSService();