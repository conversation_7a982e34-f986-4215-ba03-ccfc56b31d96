'use strict';

const express = require('express');
const { authenticate, authorize } = require('../../../middleware/auth');
const startupController = require('../controllers/startupController');

const router = express.Router();

// All routes require authentication
router.use(authenticate);

/**
 * @swagger
 * /api/v1/startup/cash-position:
 *   get:
 *     summary: Get current cash position
 *     tags: [Startup Finance]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Current cash position retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                 data:
 *                   type: object
 *                   properties:
 *                     currentBalance:
 *                       type: number
 *                     currency:
 *                       type: string
 *                     lastUpdated:
 *                       type: string
 *                       format: date-time
 *                     history:
 *                       type: array
 *                       items:
 *                         type: object
 */
router.get('/cash-position', startupController.getCashPosition);

/**
 * @swagger
 * /api/v1/startup/cash-position:
 *   post:
 *     summary: Update cash position manually
 *     tags: [Startup Finance]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - currentBalance
 *             properties:
 *               currentBalance:
 *                 type: number
 *               date:
 *                 type: string
 *                 format: date
 *               notes:
 *                 type: string
 *     responses:
 *       201:
 *         description: Cash position updated successfully
 */
router.post('/cash-position', startupController.updateCashPosition);

/**
 * @swagger
 * /api/v1/startup/burn-rate:
 *   get:
 *     summary: Get monthly burn rate calculation
 *     tags: [Startup Finance]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: month
 *         schema:
 *           type: string
 *           format: date
 *         description: Month to calculate burn rate for (defaults to current month)
 *     responses:
 *       200:
 *         description: Burn rate calculation retrieved successfully
 */
router.get('/burn-rate', startupController.getBurnRate);

/**
 * @swagger
 * /api/v1/startup/runway:
 *   get:
 *     summary: Get runway calculation
 *     tags: [Startup Finance]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Runway calculation retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                 data:
 *                   type: object
 *                   properties:
 *                     currentCash:
 *                       type: number
 *                     avgMonthlyBurn:
 *                       type: number
 *                     runwayMonths:
 *                       type: integer
 *                     runwayDays:
 *                       type: integer
 *                     lastUpdated:
 *                       type: string
 *                       format: date-time
 */
router.get('/runway', startupController.getRunway);

/**
 * @swagger
 * /api/v1/startup/cash-flow-projection:
 *   get:
 *     summary: Get cash flow projections
 *     tags: [Startup Finance]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: months
 *         schema:
 *           type: integer
 *           default: 6
 *         description: Number of months to project
 *     responses:
 *       200:
 *         description: Cash flow projections retrieved successfully
 */
router.get('/cash-flow-projection', startupController.getCashFlowProjections);

/**
 * @swagger
 * /api/v1/startup/runway-alerts:
 *   get:
 *     summary: Get active runway alerts
 *     tags: [Startup Finance]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Active runway alerts retrieved successfully
 */
router.get('/runway-alerts', startupController.getRunwayAlerts);

/**
 * @swagger
 * /api/v1/startup/runway-alerts/{alertId}/dismiss:
 *   post:
 *     summary: Dismiss a runway alert
 *     tags: [Startup Finance]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: alertId
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *     responses:
 *       200:
 *         description: Alert dismissed successfully
 */
router.post('/runway-alerts/:alertId/dismiss', startupController.dismissRunwayAlert);

/**
 * @swagger
 * /api/v1/startup/dashboard/overview:
 *   get:
 *     summary: Get startup dashboard overview
 *     tags: [Startup Finance]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Dashboard overview retrieved successfully
 */
router.get('/dashboard/overview', startupController.getDashboardOverview);

/**
 * @swagger
 * /api/v1/startup/dashboard/health-score:
 *   get:
 *     summary: Get financial health score
 *     tags: [Startup Finance]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Health score retrieved successfully
 */
router.get('/dashboard/health-score', startupController.getHealthScore);

/**
 * @swagger
 * /api/v1/startup/scenarios:
 *   get:
 *     summary: Get all scenarios for the company
 *     tags: [Scenario Planning]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Scenarios retrieved successfully
 */
router.get('/scenarios', startupController.getScenarios);

/**
 * @swagger
 * /api/v1/startup/scenarios:
 *   post:
 *     summary: Create a new scenario
 *     tags: [Scenario Planning]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - scenarioName
 *               - scenarioType
 *               - parameters
 *             properties:
 *               scenarioName:
 *                 type: string
 *               scenarioType:
 *                 type: string
 *                 enum: [hiring, revenue_growth, cost_reduction, fundraising, custom]
 *               parameters:
 *                 type: object
 *               description:
 *                 type: string
 *     responses:
 *       201:
 *         description: Scenario created successfully
 */
router.post('/scenarios', startupController.createScenario);

/**
 * @swagger
 * /api/v1/startup/scenarios/calculate:
 *   post:
 *     summary: Calculate scenario impact
 *     tags: [Scenario Planning]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - scenarioType
 *               - parameters
 *             properties:
 *               scenarioType:
 *                 type: string
 *                 enum: [hiring, revenue_growth, cost_reduction, fundraising]
 *               parameters:
 *                 type: object
 *     responses:
 *       200:
 *         description: Scenario impact calculated successfully
 */
router.post('/scenarios/calculate', startupController.calculateScenarioImpact);

/**
 * @swagger
 * /api/v1/startup/scenarios/{scenarioId}:
 *   delete:
 *     summary: Delete a scenario
 *     tags: [Scenario Planning]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: scenarioId
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *     responses:
 *       200:
 *         description: Scenario deleted successfully
 */
router.delete('/scenarios/:scenarioId', startupController.deleteScenario);

module.exports = router;