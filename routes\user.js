const express = require('express');
const { body } = require('express-validator');
const { authenticateToken } = require('../middleware/auth');
const { handleValidationErrors } = require('../middleware/validation');
const { pool } = require('../config/database');
const logger = require('../utils/logger');

const router = express.Router();

// Get user profile
router.get('/profile', authenticateToken, async (req, res, next) => {
    try {
        const userId = req.userId;

        const result = await pool.query(`
            SELECT 
                id,
                email,
                subscription_tier,
                subscription_expires_at,
                created_at,
                (SELECT COUNT(*) FROM accounts WHERE user_id = $1 AND is_active = true) as account_count,
                (SELECT COUNT(*) FROM transactions WHERE user_id = $1) as transaction_count,
                (SELECT COUNT(*) FROM categories WHERE user_id = $1 AND is_active = true AND is_default = false) as custom_category_count
            FROM users 
            WHERE id = $1
        `, [userId]);

        if (result.rows.length === 0) {
            return res.status(404).json({ error: 'User not found' });
        }

        const user = result.rows[0];

        res.json({
            user: {
                id: user.id,
                email: user.email,
                subscription_tier: user.subscription_tier,
                subscription_expires_at: user.subscription_expires_at,
                created_at: user.created_at,
                stats: {
                    account_count: parseInt(user.account_count),
                    transaction_count: parseInt(user.transaction_count),
                    custom_category_count: parseInt(user.custom_category_count)
                }
            }
        });
    } catch (error) {
        next(error);
    }
});

// Update user profile
router.put('/profile', authenticateToken, [
    body('email')
        .optional()
        .isEmail()
        .normalizeEmail()
        .withMessage('Must be a valid email address'),
    handleValidationErrors
], async (req, res, next) => {
    try {
        const userId = req.userId;
        const { email } = req.body;

        if (!email) {
            return res.status(400).json({ error: 'No fields to update' });
        }

        // Check if email is already taken
        const emailCheck = await pool.query(
            'SELECT id FROM users WHERE email = $1 AND id != $2',
            [email, userId]
        );

        if (emailCheck.rows.length > 0) {
            return res.status(409).json({ error: 'Email already in use' });
        }

        // Update user
        const result = await pool.query(`
            UPDATE users 
            SET email = $2, updated_at = NOW() 
            WHERE id = $1 
            RETURNING id, email, subscription_tier, subscription_expires_at, created_at, updated_at
        `, [userId, email]);

        const user = result.rows[0];

        logger.info(`User profile updated: ${userId}`);

        res.json({
            message: 'Profile updated successfully',
            user
        });
    } catch (error) {
        next(error);
    }
});

// Upgrade to premium (mock implementation)
router.post('/upgrade-premium', authenticateToken, async (req, res, next) => {
    try {
        const userId = req.userId;
        const { payment_method_id } = req.body;

        if (!payment_method_id) {
            return res.status(400).json({ error: 'Payment method required' });
        }

        // In a real implementation, you would:
        // 1. Process payment with Stripe
        // 2. Handle subscription creation
        // 3. Set up recurring billing

        // For this example, we'll just update the user's subscription
        const expiresAt = new Date();
        expiresAt.setMonth(expiresAt.getMonth() + 1); // 1 month from now

        const result = await pool.query(`
            UPDATE users 
            SET 
                subscription_tier = 'premium',
                subscription_expires_at = $2,
                updated_at = NOW()
            WHERE id = $1 
            RETURNING id, email, subscription_tier, subscription_expires_at
        `, [userId, expiresAt]);

        const user = result.rows[0];

        logger.info(`User upgraded to premium: ${userId}`);

        res.json({
            message: 'Successfully upgraded to premium',
            user,
            billing: {
                next_billing_date: expiresAt,
                amount: 9.99,
                currency: 'USD'
            }
        });
    } catch (error) {
        next(error);
    }
});

// Cancel premium subscription
router.post('/cancel-premium', authenticateToken, async (req, res, next) => {
    try {
        const userId = req.userId;

        // Check if user has premium subscription
        const userCheck = await pool.query(
            'SELECT subscription_tier, subscription_expires_at FROM users WHERE id = $1',
            [userId]
        );

        if (userCheck.rows.length === 0) {
            return res.status(404).json({ error: 'User not found' });
        }

        const user = userCheck.rows[0];

        if (user.subscription_tier !== 'premium') {
            return res.status(400).json({ error: 'User does not have premium subscription' });
        }

        // In a real implementation, you would cancel the Stripe subscription
        // For now, we'll just mark it for cancellation at the end of the billing period

        logger.info(`Premium subscription cancelled: ${userId}`);

        res.json({
            message: 'Premium subscription cancelled',
            cancellation_date: user.subscription_expires_at,
            note: 'You will continue to have premium access until your current billing period ends'
        });
    } catch (error) {
        next(error);
    }
});

// Get user's subscription status and limits
router.get('/subscription-status', authenticateToken, async (req, res, next) => {
    try {
        const userId = req.userId;

        const result = await pool.query(`
            SELECT 
                subscription_tier,
                subscription_expires_at,
                (SELECT COUNT(*) FROM accounts WHERE user_id = $1 AND is_active = true) as account_count,
                (SELECT COUNT(*) FROM categories WHERE user_id = $1 AND is_active = true AND is_default = false) as custom_category_count,
                (SELECT COUNT(*) FROM statement_uploads WHERE user_id = $1 AND created_at >= DATE_TRUNC('month', CURRENT_DATE)) as monthly_uploads
            FROM users 
            WHERE id = $1
        `, [userId]);

        const user = result.rows[0];
        const accountCount = parseInt(user.account_count);
        const customCategoryCount = parseInt(user.custom_category_count);
        const monthlyUploads = parseInt(user.monthly_uploads);

        const limits = {
            free: {
                accounts: 3,
                custom_categories: 5,
                monthly_uploads: 1, // UPDATED: Changed from 10 to 1
                features: ['basic_transactions', 'simple_budgets', 'basic_reports', 'basic_statement_import']
            },
            premium: {
                accounts: -1, // unlimited
                custom_categories: -1, // unlimited
                monthly_uploads: -1, // unlimited
                features: [
                    'unlimited_accounts',
                    'unlimited_categories',
                    'unlimited_statement_imports',
                    'advanced_analytics',
                    'predictive_alerts',
                    'account_sharing',
                    'excel_export',
                    'priority_support',
                    'advanced_ai_categorization'
                ]
            }
        };

        const currentLimits = limits[user.subscription_tier];
        const isPremium = user.subscription_tier === 'premium';
        const isExpired = isPremium && user.subscription_expires_at && user.subscription_expires_at < new Date();

        res.json({
            subscription: {
                tier: user.subscription_tier,
                is_premium: isPremium && !isExpired,
                expires_at: user.subscription_expires_at,
                is_expired: isExpired
            },
            usage: {
                accounts: {
                    current: accountCount,
                    limit: currentLimits.accounts,
                    percentage: currentLimits.accounts > 0 ? (accountCount / currentLimits.accounts * 100) : 0
                },
                custom_categories: {
                    current: customCategoryCount,
                    limit: currentLimits.custom_categories,
                    percentage: currentLimits.custom_categories > 0 ? (customCategoryCount / currentLimits.custom_categories * 100) : 0
                },
                monthly_uploads: {
                    current: monthlyUploads,
                    limit: currentLimits.monthly_uploads,
                    percentage: currentLimits.monthly_uploads > 0 ? (monthlyUploads / currentLimits.monthly_uploads * 100) : 0,
                    remaining: currentLimits.monthly_uploads > 0 ? Math.max(0, currentLimits.monthly_uploads - monthlyUploads) : -1
                }
            },
            features: currentLimits.features,
            limits: currentLimits
        });
    } catch (error) {
        next(error);
    }
});

module.exports = router;