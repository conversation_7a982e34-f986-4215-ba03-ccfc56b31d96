'use strict';

module.exports = (sequelize, DataTypes) => {
  const Category = sequelize.define('Category', {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true
    },
    name: {
      type: DataTypes.STRING,
      allowNull: false
    },
    code: {
      type: DataTypes.STRING
    },
    description: {
      type: DataTypes.TEXT
    },
    parentCategoryId: {
      type: DataTypes.UUID,
      references: {
        model: 'Categories',
        key: 'id'
      }
    },
    companyId: {
      type: DataTypes.UUID,
      references: {
        model: 'Companies',
        key: 'id'
      }
    },
    accountingCode: {
      type: DataTypes.STRING
    },
    budgetLimit: {
      type: DataTypes.DECIMAL(19, 4)
    },
    isActive: {
      type: DataTypes.BOOLEAN,
      defaultValue: true
    },
    requiresReceipt: {
      type: DataTypes.BOOLEAN,
      defaultValue: false
    },
    approvalThreshold: {
      type: DataTypes.DECIMAL(19, 4)
    },
    taxDeductible: {
      type: DataTypes.BOOLEAN,
      defaultValue: false
    }
  }, {
    timestamps: true,
    paranoid: true // Soft deletes
  });

  Category.associate = (models) => {
    Category.belongsTo(models.Company, {
      foreignKey: 'companyId',
      as: 'company'
    });
    
    Category.belongsTo(models.Category, {
      foreignKey: 'parentCategoryId',
      as: 'parentCategory'
    });
    
    Category.hasMany(models.Category, {
      foreignKey: 'parentCategoryId',
      as: 'subcategories'
    });
    
    Category.hasMany(models.Expense, {
      foreignKey: 'categoryId',
      as: 'expenses'
    });
    
    Category.hasMany(models.Budget, {
      foreignKey: 'categoryId',
      as: 'budgets'
    });
  };

  return Category;
};