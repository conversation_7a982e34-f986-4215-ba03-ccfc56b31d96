'use strict';

const express = require('express');
const { authenticate } = require('../../../middleware/auth');
const dashboardController = require('../controllers/dashboardController');

const router = express.Router();

// All routes require authentication
router.use(authenticate);

/**
 * @swagger
 * /api/v1/dashboard/summary:
 *   get:
 *     summary: Get complete dashboard summary with all key metrics
 *     tags: [Dashboard]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Dashboard summary retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                 data:
 *                   type: object
 *                   properties:
 *                     spending:
 *                       type: object
 *                     approvals:
 *                       type: object
 *                     budgets:
 *                       type: array
 *                     recentActivity:
 *                       type: array
 *                     notifications:
 *                       type: object
 *                     alerts:
 *                       type: array
 *                     startup:
 *                       type: object
 */
router.get('/summary', dashboardController.getDashboardSummary);

/**
 * @swagger
 * /api/v1/dashboard/real-time-stats:
 *   get:
 *     summary: Get real-time statistics
 *     tags: [Dashboard]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Real-time stats retrieved successfully
 */
router.get('/real-time-stats', dashboardController.getRealTimeStats);

/**
 * @swagger
 * /api/v1/dashboard/performance-metrics:
 *   get:
 *     summary: Get system performance metrics
 *     tags: [Dashboard]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Performance metrics retrieved successfully
 */
router.get('/performance-metrics', dashboardController.getPerformanceMetrics);

module.exports = router;