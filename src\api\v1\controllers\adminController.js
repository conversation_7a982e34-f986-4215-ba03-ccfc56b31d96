'use strict';

const { Op } = require('sequelize');
const { 
  User, 
  Company, 
  Department, 
  Expense, 
  Approval, 
  Budget,
  AuditLog,
  UserSession
} = require('../../../models');
const { ApiError } = require('../../../utils/apiError');
const auditService = require('../../../services/auditService');
const logger = require('../../../utils/logger');

/**
 * Get all users with filtering and pagination
 */
const getUsers = async (req, res, next) => {
  try {
    const {
      page = 1,
      limit = 20,
      search,
      role,
      department,
      status,
      sortBy = 'createdAt',
      order = 'DESC'
    } = req.query;

    const where = {};
    
    // Apply filters
    if (search) {
      where[Op.or] = [
        { firstName: { [Op.iLike]: `%${search}%` } },
        { lastName: { [Op.iLike]: `%${search}%` } },
        { email: { [Op.iLike]: `%${search}%` } }
      ];
    }
    
    if (role) where.role = role;
    if (department) where.departmentId = department;
    if (status === 'active') where.isActive = true;
    if (status === 'inactive') where.isActive = false;

    const offset = (page - 1) * limit;

    const { count, rows } = await User.findAndCountAll({
      where,
      include: [
        {
          model: Company,
          as: 'company',
          attributes: ['id', 'name']
        },
        {
          model: Department,
          as: 'department',
          attributes: ['id', 'name']
        }
      ],
      attributes: { exclude: ['password'] },
      order: [[sortBy, order.toUpperCase()]],
      limit: parseInt(limit),
      offset
    });

    // Get additional stats for each user
    const usersWithStats = await Promise.all(
      rows.map(async (user) => {
        const [expenseCount, totalSpent, lastActivity] = await Promise.all([
          Expense.count({ where: { userId: user.id } }),
          Expense.sum('amountInBaseCurrency', { 
            where: { userId: user.id, status: 'approved' } 
          }),
          UserSession.findOne({
            where: { userId: user.id, isActive: true },
            order: [['lastActivity', 'DESC']],
            attributes: ['lastActivity']
          })
        ]);

        return {
          ...user.toJSON(),
          stats: {
            expenseCount,
            totalSpent: totalSpent || 0,
            lastActivity: lastActivity?.lastActivity || user.lastLogin
          }
        };
      })
    );

    res.status(200).json({
      status: 'success',
      data: {
        users: usersWithStats,
        pagination: {
          total: count,
          page: parseInt(page),
          limit: parseInt(limit),
          totalPages: Math.ceil(count / limit)
        }
      }
    });
  } catch (error) {
    logger.error('Error getting users:', error);
    next(error);
  }
};

/**
 * Deactivate user
 */
const deactivateUser = async (req, res, next) => {
  try {
    const { userId } = req.params;
    const { reason } = req.body;

    const user = await User.findByPk(userId);
    if (!user) {
      throw ApiError.notFound('User not found');
    }

    if (!user.isActive) {
      throw ApiError.badRequest('User is already inactive');
    }

    // Deactivate user
    user.isActive = false;
    await user.save();

    // Deactivate all user sessions
    await UserSession.update(
      { isActive: false },
      { where: { userId } }
    );

    // Log audit event
    await auditService.logEvent({
      entityType: 'user',
      entityId: userId,
      action: 'deactivate',
      changes: { isActive: false, reason },
      userId: req.user.id,
      ipAddress: req.ip,
      userAgent: req.get('User-Agent')
    });

    res.status(200).json({
      status: 'success',
      message: 'User deactivated successfully'
    });
  } catch (error) {
    logger.error('Error deactivating user:', error);
    next(error);
  }
};

/**
 * Activate user
 */
const activateUser = async (req, res, next) => {
  try {
    const { userId } = req.params;

    const user = await User.findByPk(userId);
    if (!user) {
      throw ApiError.notFound('User not found');
    }

    if (user.isActive) {
      throw ApiError.badRequest('User is already active');
    }

    // Activate user
    user.isActive = true;
    await user.save();

    // Log audit event
    await auditService.logEvent({
      entityType: 'user',
      entityId: userId,
      action: 'activate',
      changes: { isActive: true },
      userId: req.user.id,
      ipAddress: req.ip,
      userAgent: req.get('User-Agent')
    });

    res.status(200).json({
      status: 'success',
      message: 'User activated successfully'
    });
  } catch (error) {
    logger.error('Error activating user:', error);
    next(error);
  }
};

/**
 * Get system statistics
 */
const getSystemStats = async (req, res, next) => {
  try {
    const { timeframe = '30d' } = req.query;
    
    // Calculate date range based on timeframe
    const now = new Date();
    let startDate;
    
    switch (timeframe) {
      case '7d':
        startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
        break;
      case '30d':
        startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
        break;
      case '90d':
        startDate = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000);
        break;
      default:
        startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
    }

    const [
      totalUsers,
      activeUsers,
      totalExpenses,
      totalSpent,
      pendingApprovals,
      companiesCount,
      recentActivity,
      topSpenders,
      expensesByStatus,
      monthlyTrends
    ] = await Promise.all([
      User.count(),
      User.count({ where: { isActive: true } }),
      Expense.count(),
      Expense.sum('amountInBaseCurrency', { where: { status: 'approved' } }),
      Approval.count({ where: { status: 'pending' } }),
      Company.count({ where: { status: 'active' } }),
      getRecentActivity(startDate),
      getTopSpenders(10),
      getExpensesByStatus(),
      getMonthlyTrends(startDate)
    ]);

    const stats = {
      overview: {
        totalUsers,
        activeUsers,
        totalExpenses,
        totalSpent: totalSpent || 0,
        pendingApprovals,
        companiesCount
      },
      activity: recentActivity,
      topSpenders,
      expensesByStatus,
      trends: monthlyTrends,
      systemHealth: {
        uptime: process.uptime(),
        memory: process.memoryUsage(),
        nodeVersion: process.version
      },
      generatedAt: new Date()
    };

    res.status(200).json({
      status: 'success',
      data: stats
    });
  } catch (error) {
    logger.error('Error getting system stats:', error);
    next(error);
  }
};

/**
 * Perform bulk operations on users
 */
const bulkOperations = async (req, res, next) => {
  try {
    const { operation, userIds, data = {} } = req.body;

    if (!operation || !userIds || !Array.isArray(userIds)) {
      throw ApiError.badRequest('Operation and userIds array are required');
    }

    let results = { success: 0, failed: 0, errors: [] };

    switch (operation) {
      case 'deactivate':
        results = await bulkDeactivateUsers(userIds, req.user.id, req);
        break;
      case 'activate':
        results = await bulkActivateUsers(userIds, req.user.id, req);
        break;
      case 'updateRole':
        if (!data.role) {
          throw ApiError.badRequest('Role is required for updateRole operation');
        }
        results = await bulkUpdateUserRoles(userIds, data.role, req.user.id, req);
        break;
      case 'updateDepartment':
        if (!data.departmentId) {
          throw ApiError.badRequest('Department ID is required for updateDepartment operation');
        }
        results = await bulkUpdateUserDepartments(userIds, data.departmentId, req.user.id, req);
        break;
      default:
        throw ApiError.badRequest('Invalid operation');
    }

    res.status(200).json({
      status: 'success',
      data: results
    });
  } catch (error) {
    logger.error('Error performing bulk operations:', error);
    next(error);
  }
};

/**
 * Get audit trail
 */
const getAuditTrail = async (req, res, next) => {
  try {
    const {
      page = 1,
      limit = 50,
      entityType,
      action,
      userId,
      startDate,
      endDate
    } = req.query;

    const filters = {
      page: parseInt(page),
      limit: parseInt(limit)
    };

    if (entityType) filters.entityType = entityType;
    if (action) filters.action = action;
    if (userId) filters.userId = userId;
    if (startDate) filters.startDate = startDate;
    if (endDate) filters.endDate = endDate;

    const auditData = await auditService.getAuditLogs(filters);

    res.status(200).json({
      status: 'success',
      data: auditData
    });
  } catch (error) {
    logger.error('Error getting audit trail:', error);
    next(error);
  }
};

// Helper functions

/**
 * Get recent activity
 */
const getRecentActivity = async (startDate) => {
  try {
    const activities = await AuditLog.findAll({
      where: {
        createdAt: { [Op.gte]: startDate }
      },
      include: [{
        model: User,
        as: 'user',
        attributes: ['firstName', 'lastName', 'email']
      }],
      order: [['createdAt', 'DESC']],
      limit: 20
    });

    return activities.map(activity => ({
      id: activity.id,
      action: activity.action,
      entityType: activity.entityType,
      user: activity.user ? 
        `${activity.user.firstName} ${activity.user.lastName}` : 
        'System',
      timestamp: activity.createdAt,
      ipAddress: activity.ipAddress
    }));
  } catch (error) {
    logger.error('Error getting recent activity:', error);
    return [];
  }
};

/**
 * Get top spenders
 */
const getTopSpenders = async (limit = 10) => {
  try {
    const topSpenders = await User.findAll({
      attributes: [
        'id',
        'firstName',
        'lastName',
        'email',
        [User.sequelize.fn('SUM', User.sequelize.col('expenses.amountInBaseCurrency')), 'totalSpent'],
        [User.sequelize.fn('COUNT', User.sequelize.col('expenses.id')), 'expenseCount']
      ],
      include: [{
        model: Expense,
        as: 'expenses',
        where: { status: 'approved' },
        attributes: []
      }],
      group: ['User.id'],
      order: [[User.sequelize.fn('SUM', User.sequelize.col('expenses.amountInBaseCurrency')), 'DESC']],
      limit
    });

    return topSpenders.map(user => ({
      id: user.id,
      name: `${user.firstName} ${user.lastName}`,
      email: user.email,
      totalSpent: parseFloat(user.dataValues.totalSpent) || 0,
      expenseCount: parseInt(user.dataValues.expenseCount) || 0
    }));
  } catch (error) {
    logger.error('Error getting top spenders:', error);
    return [];
  }
};

/**
 * Get expenses by status
 */
const getExpensesByStatus = async () => {
  try {
    const statusCounts = await Expense.findAll({
      attributes: [
        'status',
        [Expense.sequelize.fn('COUNT', '*'), 'count'],
        [Expense.sequelize.fn('SUM', Expense.sequelize.col('amountInBaseCurrency')), 'total']
      ],
      group: ['status'],
      raw: true
    });

    return statusCounts.reduce((acc, item) => {
      acc[item.status] = {
        count: parseInt(item.count),
        total: parseFloat(item.total) || 0
      };
      return acc;
    }, {});
  } catch (error) {
    logger.error('Error getting expenses by status:', error);
    return {};
  }
};

/**
 * Get monthly trends
 */
const getMonthlyTrends = async (startDate) => {
  try {
    const trends = await Expense.findAll({
      where: {
        createdAt: { [Op.gte]: startDate },
        status: 'approved'
      },
      attributes: [
        [Expense.sequelize.fn('DATE_TRUNC', 'month', Expense.sequelize.col('createdAt')), 'month'],
        [Expense.sequelize.fn('COUNT', '*'), 'count'],
        [Expense.sequelize.fn('SUM', Expense.sequelize.col('amountInBaseCurrency')), 'total']
      ],
      group: [Expense.sequelize.fn('DATE_TRUNC', 'month', Expense.sequelize.col('createdAt'))],
      order: [[Expense.sequelize.fn('DATE_TRUNC', 'month', Expense.sequelize.col('createdAt')), 'ASC']],
      raw: true
    });

    return trends.map(trend => ({
      month: trend.month,
      count: parseInt(trend.count),
      total: parseFloat(trend.total) || 0
    }));
  } catch (error) {
    logger.error('Error getting monthly trends:', error);
    return [];
  }
};

/**
 * Bulk deactivate users
 */
const bulkDeactivateUsers = async (userIds, adminId, req) => {
  const results = { success: 0, failed: 0, errors: [] };

  for (const userId of userIds) {
    try {
      const user = await User.findByPk(userId);
      if (user && user.isActive) {
        user.isActive = false;
        await user.save();

        // Deactivate sessions
        await UserSession.update(
          { isActive: false },
          { where: { userId } }
        );

        // Log audit event
        await auditService.logEvent({
          entityType: 'user',
          entityId: userId,
          action: 'bulk_deactivate',
          changes: { isActive: false },
          userId: adminId,
          ipAddress: req.ip,
          userAgent: req.get('User-Agent')
        });

        results.success++;
      } else {
        results.failed++;
        results.errors.push(`User ${userId} not found or already inactive`);
      }
    } catch (error) {
      results.failed++;
      results.errors.push(`Error deactivating user ${userId}: ${error.message}`);
    }
  }

  return results;
};

/**
 * Bulk activate users
 */
const bulkActivateUsers = async (userIds, adminId, req) => {
  const results = { success: 0, failed: 0, errors: [] };

  for (const userId of userIds) {
    try {
      const user = await User.findByPk(userId);
      if (user && !user.isActive) {
        user.isActive = true;
        await user.save();

        // Log audit event
        await auditService.logEvent({
          entityType: 'user',
          entityId: userId,
          action: 'bulk_activate',
          changes: { isActive: true },
          userId: adminId,
          ipAddress: req.ip,
          userAgent: req.get('User-Agent')
        });

        results.success++;
      } else {
        results.failed++;
        results.errors.push(`User ${userId} not found or already active`);
      }
    } catch (error) {
      results.failed++;
      results.errors.push(`Error activating user ${userId}: ${error.message}`);
    }
  }

  return results;
};

/**
 * Bulk update user roles
 */
const bulkUpdateUserRoles = async (userIds, newRole, adminId, req) => {
  const results = { success: 0, failed: 0, errors: [] };

  for (const userId of userIds) {
    try {
      const user = await User.findByPk(userId);
      if (user) {
        const oldRole = user.role;
        user.role = newRole;
        await user.save();

        // Log audit event
        await auditService.logEvent({
          entityType: 'user',
          entityId: userId,
          action: 'bulk_update_role',
          changes: { role: { from: oldRole, to: newRole } },
          userId: adminId,
          ipAddress: req.ip,
          userAgent: req.get('User-Agent')
        });

        results.success++;
      } else {
        results.failed++;
        results.errors.push(`User ${userId} not found`);
      }
    } catch (error) {
      results.failed++;
      results.errors.push(`Error updating role for user ${userId}: ${error.message}`);
    }
  }

  return results;
};

/**
 * Bulk update user departments
 */
const bulkUpdateUserDepartments = async (userIds, departmentId, adminId, req) => {
  const results = { success: 0, failed: 0, errors: [] };

  for (const userId of userIds) {
    try {
      const user = await User.findByPk(userId);
      if (user) {
        const oldDepartmentId = user.departmentId;
        user.departmentId = departmentId;
        await user.save();

        // Log audit event
        await auditService.logEvent({
          entityType: 'user',
          entityId: userId,
          action: 'bulk_update_department',
          changes: { departmentId: { from: oldDepartmentId, to: departmentId } },
          userId: adminId,
          ipAddress: req.ip,
          userAgent: req.get('User-Agent')
        });

        results.success++;
      } else {
        results.failed++;
        results.errors.push(`User ${userId} not found`);
      }
    } catch (error) {
      results.failed++;
      results.errors.push(`Error updating department for user ${userId}: ${error.message}`);
    }
  }

  return results;
};

module.exports = {
  getUsers,
  deactivateUser,
  activateUser,
  getSystemStats,
  bulkOperations,
  getAuditTrail
};