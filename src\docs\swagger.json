{"openapi": "3.0.0", "info": {"title": "Enterprise Financial Management API", "version": "1.0.0", "description": "API documentation for the Enterprise Financial Management platform with Startup Finance features", "contact": {"name": "API Support", "email": "<EMAIL>"}}, "servers": [{"url": "http://localhost:3000", "description": "Development server"}], "components": {"securitySchemes": {"bearerAuth": {"type": "http", "scheme": "bearer", "bearerFormat": "JWT"}}}, "security": [{"bearerAuth": []}], "tags": [{"name": "Authentication", "description": "API endpoints for user authentication"}, {"name": "Expenses", "description": "API endpoints for managing expenses"}, {"name": "Approvals", "description": "API endpoints for expense approvals"}, {"name": "Reports", "description": "API endpoints for financial reports"}, {"name": "Banking", "description": "API endpoints for bank integrations"}, {"name": "Budgets", "description": "API endpoints for budget management"}, {"name": "Startup Finance", "description": "API endpoints for startup financial management"}, {"name": "Scenario Planning", "description": "API endpoints for financial scenario planning"}, {"name": "Dashboard", "description": "API endpoints for dashboard data"}, {"name": "Admin", "description": "API endpoints for admin management"}, {"name": "Notifications", "description": "API endpoints for notifications"}, {"name": "AI Features", "description": "API endpoints for AI-powered features"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "description": "API endpoints for currency management"}, {"name": "Files", "description": "API endpoints for file management"}], "paths": {"/health": {"get": {"summary": "Health check endpoint", "tags": ["System"], "responses": {"200": {"description": "System is healthy"}}}}, "/api/v1/auth/login": {"post": {"summary": "User login", "tags": ["Authentication"], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["email", "password"], "properties": {"email": {"type": "string", "format": "email"}, "password": {"type": "string"}}}}}}, "responses": {"200": {"description": "Login successful"}, "401": {"description": "Invalid credentials"}}}}, "/api/v1/expenses": {"get": {"summary": "Get all expenses", "tags": ["Expenses"], "security": [{"bearerAuth": []}], "responses": {"200": {"description": "List of expenses"}}}, "post": {"summary": "Create new expense", "tags": ["Expenses"], "security": [{"bearerAuth": []}], "responses": {"201": {"description": "Expense created"}}}}}}