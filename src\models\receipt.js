'use strict';

module.exports = (sequelize, DataTypes) => {
  const Receipt = sequelize.define('Receipt', {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true
    },
    expenseId: {
      type: DataTypes.UUID,
      allowNull: false,
      references: {
        model: 'Expenses',
        key: 'id'
      }
    },
    fileUrl: {
      type: DataTypes.STRING,
      allowNull: false
    },
    fileName: {
      type: DataTypes.STRING
    },
    fileType: {
      type: DataTypes.STRING
    },
    fileSize: {
      type: DataTypes.INTEGER
    },
    uploadedBy: {
      type: DataTypes.UUID,
      references: {
        model: 'Users',
        key: 'id'
      }
    },
    isProcessed: {
      type: DataTypes.BOOLEAN,
      defaultValue: false
    },
    ocrText: {
      type: DataTypes.TEXT
    },
    ocrData: {
      type: DataTypes.JSONB
    },
    ocrConfidence: {
      type: DataTypes.DECIMAL(5, 2)
    },
    ocrProcessedAt: {
      type: DataTypes.DATE
    },
    extractedAmount: {
      type: DataTypes.DECIMAL(19, 4)
    },
    extractedDate: {
      type: DataTypes.DATEONLY
    },
    extractedMerchant: {
      type: DataTypes.STRING
    },
    extractedTax: {
      type: DataTypes.DECIMAL(19, 4)
    }
  }, {
    timestamps: true,
    indexes: [
      { fields: ['expenseId'] },
      { fields: ['isProcessed'] }
    ]
  });

  Receipt.associate = (models) => {
    Receipt.belongsTo(models.Expense, {
      foreignKey: 'expenseId',
      as: 'expense'
    });
    
    Receipt.belongsTo(models.User, {
      foreignKey: 'uploadedBy',
      as: 'uploader'
    });
  };

  return Receipt;
};