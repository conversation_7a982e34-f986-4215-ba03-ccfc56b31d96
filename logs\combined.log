{"address":"127.0.0.1","code":"ECONNREFUSED","errno":-111,"level":"error","message":"Database connection failed: connect ECONNREFUSED 127.0.0.1:5432","port":5432,"service":"finance-manager-api","stack":"Error: connect ECONNREFUSED 127.0.0.1:5432\n    at eval (file:///home/<USER>/node_modules/pg-pool/index.js#cjs:45:11)\n    at https://zp1v56uxy8rdx5ypatb0ockcb9tr6a-oci3.w-credentialless-staticblitz.com/blitz.b980e309.js:31:29698","syscall":"connect","timestamp":"2025-06-24T13:24:56.824Z"}
{"level":"info","message":"Starting database migration...","service":"finance-manager-api","timestamp":"2025-06-24T16:21:07.632Z"}
{"address":"127.0.0.1","code":"ECONNREFUSED","errno":-111,"level":"error","message":"Database connection failed: connect ECONNREFUSED 127.0.0.1:5432","port":5432,"service":"finance-manager-api","stack":"Error: connect ECONNREFUSED 127.0.0.1:5432\n    at eval (file:///home/<USER>/node_modules/pg-pool/index.js#cjs:45:11)\n    at https://zp1v56uxy8rdx5ypatb0ockcb9tr6a-oci3.w-credentialless-staticblitz.com/blitz.b980e309.js:31:29698","syscall":"connect","timestamp":"2025-06-25T14:09:21.404Z"}
{"address":"127.0.0.1","code":"ECONNREFUSED","errno":-111,"level":"error","message":"Database connection failed: connect ECONNREFUSED 127.0.0.1:5432","port":5432,"service":"finance-manager-api","stack":"Error: connect ECONNREFUSED 127.0.0.1:5432\n    at eval (file:///home/<USER>/node_modules/pg-pool/index.js#cjs:45:11)\n    at https://zp1v56uxy8rdx5ypatb0ockcb9tr6a-oci3.w-credentialless-staticblitz.com/blitz.b980e309.js:31:29698","syscall":"connect","timestamp":"2025-06-25T14:48:51.693Z"}
{"address":"127.0.0.1","code":"ECONNREFUSED","errno":-111,"level":"error","message":"Database connection failed: connect ECONNREFUSED 127.0.0.1:5432","port":5432,"service":"finance-manager-api","stack":"Error: connect ECONNREFUSED 127.0.0.1:5432\n    at eval (file:///home/<USER>/node_modules/pg-pool/index.js#cjs:45:11)\n    at https://zp1v56uxy8rdx5ypatb0ockcb9tr6a-oci3.w-credentialless-staticblitz.com/blitz.b980e309.js:31:29698","syscall":"connect","timestamp":"2025-06-25T14:50:39.277Z"}
{"address":"127.0.0.1","code":"ECONNREFUSED","errno":-111,"level":"error","message":"Database connection failed: connect ECONNREFUSED 127.0.0.1:5432","port":5432,"service":"finance-manager-api","stack":"Error: connect ECONNREFUSED 127.0.0.1:5432\n    at eval (file:///home/<USER>/node_modules/pg-pool/index.js#cjs:45:11)\n    at https://zp1v56uxy8rdx5ypatb0ockcb9tr6a-oci3.w-credentialless-staticblitz.com/blitz.b980e309.js:31:29698","syscall":"connect","timestamp":"2025-06-25T14:51:57.162Z"}
{"address":"127.0.0.1","code":"ECONNREFUSED","errno":-111,"level":"error","message":"Database connection failed: connect ECONNREFUSED 127.0.0.1:5432","port":5432,"service":"finance-manager-api","stack":"Error: connect ECONNREFUSED 127.0.0.1:5432\n    at eval (file:///home/<USER>/node_modules/pg-pool/index.js#cjs:45:11)\n    at https://zp1v56uxy8rdx5ypatb0ockcb9tr6a-oci3.w-credentialless-staticblitz.com/blitz.b980e309.js:31:29698","syscall":"connect","timestamp":"2025-06-25T14:53:54.071Z"}
{"cause":{},"level":"error","message":"Database connection failed: Connection terminated due to connection timeout","service":"finance-manager-api","stack":"Error: Connection terminated due to connection timeout\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async connectDB (C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\config\\database.js:17:24)\n    at async startServer (C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\server.js:90:9)","timestamp":"2025-06-25T15:31:52.470Z"}
{"address":"::1","code":"ECONNREFUSED","errno":-4078,"level":"error","message":"Database connection failed: connect ECONNREFUSED ::1:5432","port":5432,"service":"finance-manager-api","stack":"Error: connect ECONNREFUSED ::1:5432\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async connectDB (C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\config\\database.js:17:24)\n    at async startServer (C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\server.js:90:9)","syscall":"connect","timestamp":"2025-06-25T15:32:32.757Z"}
{"address":"::1","code":"ECONNREFUSED","errno":-4078,"level":"error","message":"Database connection failed: connect ECONNREFUSED ::1:5432","port":5432,"service":"finance-manager-api","stack":"Error: connect ECONNREFUSED ::1:5432\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async connectDB (C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\config\\database.js:17:24)\n    at async startServer (C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\server.js:92:13)","syscall":"connect","timestamp":"2025-06-25T15:33:09.965Z"}
{"level":"warn","message":"Database connection failed, continuing in development mode:","service":"finance-manager-api","timestamp":"2025-06-25T15:33:09.973Z"}
{"address":"::1","code":"ECONNREFUSED","errno":-4078,"level":"error","message":"Redis Client Error: connect ECONNREFUSED ::1:6379","port":6379,"service":"finance-manager-api","stack":"Error: connect ECONNREFUSED ::1:6379\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1615:16)","syscall":"connect","timestamp":"2025-06-25T15:33:09.997Z"}
{"address":"::1","code":"ECONNREFUSED","errno":-4078,"level":"error","message":"Redis Client Error: connect ECONNREFUSED ::1:6379","port":6379,"service":"finance-manager-api","stack":"Error: connect ECONNREFUSED ::1:6379\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1615:16)","syscall":"connect","timestamp":"2025-06-25T15:33:10.005Z"}
{"address":"::1","code":"ECONNREFUSED","errno":-4078,"level":"error","message":"Redis Client Error: connect ECONNREFUSED ::1:6379","port":6379,"service":"finance-manager-api","stack":"Error: connect ECONNREFUSED ::1:6379\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1615:16)","syscall":"connect","timestamp":"2025-06-25T15:33:10.059Z"}
{"address":"::1","code":"ECONNREFUSED","errno":-4078,"level":"error","message":"Redis Client Error: connect ECONNREFUSED ::1:6379","port":6379,"service":"finance-manager-api","stack":"Error: connect ECONNREFUSED ::1:6379\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1615:16)","syscall":"connect","timestamp":"2025-06-25T15:33:10.164Z"}
{"address":"::1","code":"ECONNREFUSED","errno":-4078,"level":"error","message":"Redis Client Error: connect ECONNREFUSED ::1:6379","port":6379,"service":"finance-manager-api","stack":"Error: connect ECONNREFUSED ::1:6379\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1615:16)","syscall":"connect","timestamp":"2025-06-25T15:33:10.320Z"}
{"address":"::1","code":"ECONNREFUSED","errno":-4078,"level":"error","message":"Redis Client Error: connect ECONNREFUSED ::1:6379","port":6379,"service":"finance-manager-api","stack":"Error: connect ECONNREFUSED ::1:6379\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1615:16)","syscall":"connect","timestamp":"2025-06-25T15:33:10.540Z"}
{"address":"::1","code":"ECONNREFUSED","errno":-4078,"level":"error","message":"Redis Client Error: connect ECONNREFUSED ::1:6379","port":6379,"service":"finance-manager-api","stack":"Error: connect ECONNREFUSED ::1:6379\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1615:16)","syscall":"connect","timestamp":"2025-06-25T15:33:10.804Z"}
{"address":"::1","code":"ECONNREFUSED","errno":-4078,"level":"error","message":"Redis Client Error: connect ECONNREFUSED ::1:6379","port":6379,"service":"finance-manager-api","stack":"Error: connect ECONNREFUSED ::1:6379\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1615:16)","syscall":"connect","timestamp":"2025-06-25T15:33:11.114Z"}
{"address":"::1","code":"ECONNREFUSED","errno":-4078,"level":"error","message":"Redis Client Error: connect ECONNREFUSED ::1:6379","port":6379,"service":"finance-manager-api","stack":"Error: connect ECONNREFUSED ::1:6379\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1615:16)","syscall":"connect","timestamp":"2025-06-25T15:33:11.477Z"}
{"address":"::1","code":"ECONNREFUSED","errno":-4078,"level":"error","message":"Redis Client Error: connect ECONNREFUSED ::1:6379","port":6379,"service":"finance-manager-api","stack":"Error: connect ECONNREFUSED ::1:6379\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1615:16)","syscall":"connect","timestamp":"2025-06-25T15:33:11.884Z"}
{"address":"::1","code":"ECONNREFUSED","errno":-4078,"level":"error","message":"Redis Client Error: connect ECONNREFUSED ::1:6379","port":6379,"service":"finance-manager-api","stack":"Error: connect ECONNREFUSED ::1:6379\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1615:16)","syscall":"connect","timestamp":"2025-06-25T15:33:12.352Z"}
{"address":"::1","code":"ECONNREFUSED","errno":-4078,"level":"error","message":"Redis Client Error: connect ECONNREFUSED ::1:6379","port":6379,"service":"finance-manager-api","stack":"Error: connect ECONNREFUSED ::1:6379\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1615:16)","syscall":"connect","timestamp":"2025-06-25T15:33:12.869Z"}
{"address":"::1","code":"ECONNREFUSED","errno":-4078,"level":"error","message":"Redis Client Error: connect ECONNREFUSED ::1:6379","port":6379,"service":"finance-manager-api","stack":"Error: connect ECONNREFUSED ::1:6379\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1615:16)","syscall":"connect","timestamp":"2025-06-25T15:33:13.382Z"}
{"address":"::1","code":"ECONNREFUSED","errno":-4078,"level":"error","message":"Redis Client Error: connect ECONNREFUSED ::1:6379","port":6379,"service":"finance-manager-api","stack":"Error: connect ECONNREFUSED ::1:6379\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1615:16)","syscall":"connect","timestamp":"2025-06-25T15:33:13.900Z"}
{"address":"::1","code":"ECONNREFUSED","errno":-4078,"level":"error","message":"Redis Client Error: connect ECONNREFUSED ::1:6379","port":6379,"service":"finance-manager-api","stack":"Error: connect ECONNREFUSED ::1:6379\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1615:16)","syscall":"connect","timestamp":"2025-06-25T15:33:14.416Z"}
{"address":"::1","code":"ECONNREFUSED","errno":-4078,"level":"error","message":"Redis Client Error: connect ECONNREFUSED ::1:6379","port":6379,"service":"finance-manager-api","stack":"Error: connect ECONNREFUSED ::1:6379\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1615:16)","syscall":"connect","timestamp":"2025-06-25T15:33:14.923Z"}
{"address":"::1","code":"ECONNREFUSED","errno":-4078,"level":"error","message":"Redis Client Error: connect ECONNREFUSED ::1:6379","port":6379,"service":"finance-manager-api","stack":"Error: connect ECONNREFUSED ::1:6379\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1615:16)","syscall":"connect","timestamp":"2025-06-25T15:33:15.435Z"}
{"address":"::1","code":"ECONNREFUSED","errno":-4078,"level":"error","message":"Redis Client Error: connect ECONNREFUSED ::1:6379","port":6379,"service":"finance-manager-api","stack":"Error: connect ECONNREFUSED ::1:6379\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1615:16)","syscall":"connect","timestamp":"2025-06-25T15:33:15.953Z"}
{"address":"::1","code":"ECONNREFUSED","errno":-4078,"level":"error","message":"Redis Client Error: connect ECONNREFUSED ::1:6379","port":6379,"service":"finance-manager-api","stack":"Error: connect ECONNREFUSED ::1:6379\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1615:16)","syscall":"connect","timestamp":"2025-06-25T15:33:16.470Z"}
{"address":"::1","code":"ECONNREFUSED","errno":-4078,"level":"error","message":"Redis Client Error: connect ECONNREFUSED ::1:6379","port":6379,"service":"finance-manager-api","stack":"Error: connect ECONNREFUSED ::1:6379\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1615:16)","syscall":"connect","timestamp":"2025-06-25T15:33:16.987Z"}
{"address":"::1","code":"ECONNREFUSED","errno":-4078,"level":"error","message":"Redis Client Error: connect ECONNREFUSED ::1:6379","port":6379,"service":"finance-manager-api","stack":"Error: connect ECONNREFUSED ::1:6379\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1615:16)","syscall":"connect","timestamp":"2025-06-25T15:33:17.505Z"}
{"address":"::1","code":"ECONNREFUSED","errno":-4078,"level":"error","message":"Redis Client Error: connect ECONNREFUSED ::1:6379","port":6379,"service":"finance-manager-api","stack":"Error: connect ECONNREFUSED ::1:6379\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1615:16)","syscall":"connect","timestamp":"2025-06-25T15:33:18.021Z"}
{"address":"::1","code":"ECONNREFUSED","errno":-4078,"level":"error","message":"Redis Client Error: connect ECONNREFUSED ::1:6379","port":6379,"service":"finance-manager-api","stack":"Error: connect ECONNREFUSED ::1:6379\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1615:16)","syscall":"connect","timestamp":"2025-06-25T15:33:18.536Z"}
{"address":"::1","code":"ECONNREFUSED","errno":-4078,"level":"error","message":"Redis Client Error: connect ECONNREFUSED ::1:6379","port":6379,"service":"finance-manager-api","stack":"Error: connect ECONNREFUSED ::1:6379\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1615:16)","syscall":"connect","timestamp":"2025-06-25T15:33:19.054Z"}
{"address":"::1","code":"ECONNREFUSED","errno":-4078,"level":"error","message":"Redis Client Error: connect ECONNREFUSED ::1:6379","port":6379,"service":"finance-manager-api","stack":"Error: connect ECONNREFUSED ::1:6379\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1615:16)","syscall":"connect","timestamp":"2025-06-25T15:33:19.559Z"}
{"address":"::1","code":"ECONNREFUSED","errno":-4078,"level":"error","message":"Redis Client Error: connect ECONNREFUSED ::1:6379","port":6379,"service":"finance-manager-api","stack":"Error: connect ECONNREFUSED ::1:6379\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1615:16)","syscall":"connect","timestamp":"2025-06-25T15:33:20.077Z"}
{"address":"::1","code":"ECONNREFUSED","errno":-4078,"level":"error","message":"Redis Client Error: connect ECONNREFUSED ::1:6379","port":6379,"service":"finance-manager-api","stack":"Error: connect ECONNREFUSED ::1:6379\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1615:16)","syscall":"connect","timestamp":"2025-06-25T15:33:20.591Z"}
{"cause":{},"level":"error","message":"Database connection failed: Connection terminated due to connection timeout","service":"finance-manager-api","stack":"Error: Connection terminated due to connection timeout\n    at C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async connectDB (C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\config\\database.js:17:24)\n    at async startServer (C:\\Users\\<USER>\\OneDrive\\Desktop\\project\\server.js:90:9)","timestamp":"2025-06-25T15:33:28.730Z"}
