'use strict';

const axios = require('axios');
const logger = require('../utils/logger');
const { ApiError } = require('../utils/apiError');

/**
 * AI Service for expense categorization and OCR processing
 */
class AiService {
  constructor() {
    this.openaiApiKey = process.env.OPENAI_API_KEY;
    this.geminiApiKey = process.env.GEMINI_API_KEY;
    this.openaiUrl = 'https://api.openai.com/v1';
    this.geminiUrl = 'https://generativelanguage.googleapis.com/v1beta';
  }

  /**
   * Categorize expense using AI
   * @param {Object} expenseData - Expense data to categorize
   * @returns {Promise<Object>} Categorization result
   */
  async categorizeExpense(expenseData) {
    try {
      const { description, merchant, amount } = expenseData;
      
      const prompt = `
        Categorize this business expense:
        Description: ${description}
        Merchant: ${merchant || 'Unknown'}
        Amount: $${amount}
        
        Choose from these categories:
        - Office Supplies
        - Travel & Transportation
        - Meals & Entertainment
        - Software & Technology
        - Marketing & Advertising
        - Professional Services
        - Utilities
        - Equipment
        - Training & Education
        - Other
        
        Respond with JSON: {"category": "category_name", "confidence": 0.95, "reasoning": "brief explanation"}
      `;

      // Simulate AI response for development
      const categories = [
        'Office Supplies', 'Travel & Transportation', 'Meals & Entertainment',
        'Software & Technology', 'Marketing & Advertising', 'Professional Services',
        'Utilities', 'Equipment', 'Training & Education', 'Other'
      ];
      
      const category = this._simulateAiCategorization(description, merchant, categories);
      
      return {
        category: category.name,
        confidence: category.confidence,
        reasoning: category.reasoning,
        suggestions: category.suggestions
      };
    } catch (error) {
      logger.error('Error in AI categorization:', error);
      throw new ApiError('Failed to categorize expense', 500);
    }
  }

  /**
   * Extract data from receipt using OCR
   * @param {string} imagePath - Path to receipt image
   * @returns {Promise<Object>} Extracted receipt data
   */
  async extractReceiptData(imagePath) {
    try {
      logger.info(`Processing OCR for receipt: ${imagePath}`);
      
      // Simulate OCR processing
      const extractedData = this._simulateOcrExtraction(imagePath);
      
      return {
        success: true,
        data: extractedData,
        confidence: extractedData.confidence
      };
    } catch (error) {
      logger.error('Error in OCR processing:', error);
      throw new ApiError('Failed to process receipt', 500);
    }
  }

  /**
   * Generate spending insights using AI
   * @param {Array} expenses - Array of expense data
   * @returns {Promise<Object>} Generated insights
   */
  async generateSpendingInsights(expenses) {
    try {
      const insights = {
        totalSpent: expenses.reduce((sum, exp) => sum + parseFloat(exp.amount), 0),
        topCategories: this._analyzeTopCategories(expenses),
        spendingTrends: this._analyzeSpendingTrends(expenses),
        recommendations: this._generateRecommendations(expenses),
        anomalies: this._detectAnomalies(expenses)
      };
      
      return insights;
    } catch (error) {
      logger.error('Error generating insights:', error);
      throw new ApiError('Failed to generate insights', 500);
    }
  }

  /**
   * Detect spending anomalies
   * @param {Array} expenses - Array of expense data
   * @returns {Promise<Array>} Detected anomalies
   */
  async detectAnomalies(expenses) {
    try {
      const anomalies = [];
      
      // Calculate average spending by category
      const categoryAverages = this._calculateCategoryAverages(expenses);
      
      // Detect unusual amounts
      expenses.forEach(expense => {
        const categoryAvg = categoryAverages[expense.category] || 0;
        const amount = parseFloat(expense.amount);
        
        if (amount > categoryAvg * 3) {
          anomalies.push({
            expenseId: expense.id,
            type: 'unusual_amount',
            severity: 'high',
            message: `Expense amount $${amount} is ${Math.round(amount / categoryAvg)}x higher than average for ${expense.category}`,
            confidence: 0.85
          });
        }
        
        // Detect weekend spending
        const expenseDate = new Date(expense.expenseDate);
        if (expenseDate.getDay() === 0 || expenseDate.getDay() === 6) {
          if (amount > 500) {
            anomalies.push({
              expenseId: expense.id,
              type: 'weekend_spending',
              severity: 'medium',
              message: `Large expense on weekend: $${amount}`,
              confidence: 0.7
            });
          }
        }
      });
      
      return anomalies;
    } catch (error) {
      logger.error('Error detecting anomalies:', error);
      throw new ApiError('Failed to detect anomalies', 500);
    }
  }

  /**
   * Generate budget optimization suggestions
   * @param {Object} budgetData - Current budget data
   * @param {Array} expenses - Historical expense data
   * @returns {Promise<Object>} Optimization suggestions
   */
  async optimizeBudget(budgetData, expenses) {
    try {
      const suggestions = {
        adjustments: [],
        savings: 0,
        reasoning: []
      };
      
      // Analyze spending patterns
      const categorySpending = this._analyzeCategorySpending(expenses);
      
      Object.keys(budgetData).forEach(category => {
        const budgetAmount = budgetData[category];
        const actualSpending = categorySpending[category] || 0;
        const variance = budgetAmount - actualSpending;
        
        if (variance > budgetAmount * 0.2) {
          // Over-budgeted
          const suggestedReduction = variance * 0.5;
          suggestions.adjustments.push({
            category,
            currentBudget: budgetAmount,
            suggestedBudget: budgetAmount - suggestedReduction,
            change: -suggestedReduction,
            reason: 'Consistently under-spending in this category'
          });
          suggestions.savings += suggestedReduction;
        } else if (variance < -budgetAmount * 0.1) {
          // Under-budgeted
          const suggestedIncrease = Math.abs(variance) * 1.2;
          suggestions.adjustments.push({
            category,
            currentBudget: budgetAmount,
            suggestedBudget: budgetAmount + suggestedIncrease,
            change: suggestedIncrease,
            reason: 'Consistently over-spending in this category'
          });
        }
      });
      
      return suggestions;
    } catch (error) {
      logger.error('Error optimizing budget:', error);
      throw new ApiError('Failed to optimize budget', 500);
    }
  }

  /**
   * Generate predictive cash flow analysis
   * @param {Object} financialData - Historical financial data
   * @returns {Promise<Object>} Predictive analysis
   */
  async generatePredictiveAnalysis(financialData) {
    try {
      const { expenses, revenue, cashFlow } = financialData;
      
      // Simple trend analysis
      const monthlyTrends = this._calculateMonthlyTrends(expenses);
      const projections = this._generateProjections(monthlyTrends, 6);
      
      return {
        trends: monthlyTrends,
        projections,
        recommendations: this._generateFinancialRecommendations(projections),
        riskFactors: this._identifyRiskFactors(projections)
      };
    } catch (error) {
      logger.error('Error in predictive analysis:', error);
      throw new ApiError('Failed to generate predictions', 500);
    }
  }

  // Private helper methods

  _simulateAiCategorization(description, merchant, categories) {
    // Simple keyword-based categorization for simulation
    const keywords = {
      'Office Supplies': ['office', 'supplies', 'paper', 'pen', 'staples'],
      'Travel & Transportation': ['uber', 'lyft', 'airline', 'hotel', 'gas', 'taxi'],
      'Meals & Entertainment': ['restaurant', 'coffee', 'starbucks', 'lunch', 'dinner'],
      'Software & Technology': ['software', 'saas', 'microsoft', 'adobe', 'aws'],
      'Marketing & Advertising': ['google ads', 'facebook', 'marketing', 'advertising'],
      'Professional Services': ['legal', 'consulting', 'accounting', 'professional'],
      'Utilities': ['electric', 'water', 'internet', 'phone', 'utility'],
      'Equipment': ['computer', 'laptop', 'equipment', 'hardware'],
      'Training & Education': ['training', 'course', 'education', 'conference']
    };
    
    const text = `${description} ${merchant}`.toLowerCase();
    let bestMatch = { name: 'Other', confidence: 0.5, reasoning: 'No clear category match found' };
    
    for (const [category, words] of Object.entries(keywords)) {
      const matches = words.filter(word => text.includes(word));
      if (matches.length > 0) {
        const confidence = Math.min(0.95, 0.7 + (matches.length * 0.1));
        if (confidence > bestMatch.confidence) {
          bestMatch = {
            name: category,
            confidence,
            reasoning: `Matched keywords: ${matches.join(', ')}`,
            suggestions: categories.filter(c => c !== category).slice(0, 2)
          };
        }
      }
    }
    
    return bestMatch;
  }

  _simulateOcrExtraction(imagePath) {
    // Simulate OCR extraction with realistic data
    const merchants = ['Starbucks', 'Amazon', 'Office Depot', 'Shell', 'Uber'];
    const merchant = merchants[Math.floor(Math.random() * merchants.length)];
    const amount = (Math.random() * 200 + 10).toFixed(2);
    const tax = (amount * 0.08).toFixed(2);
    
    return {
      merchant,
      date: new Date().toISOString().split('T')[0],
      amount: parseFloat(amount),
      tax: parseFloat(tax),
      currency: 'USD',
      items: [
        { description: 'Item 1', amount: parseFloat(amount) * 0.6, quantity: 1 },
        { description: 'Item 2', amount: parseFloat(amount) * 0.4, quantity: 1 }
      ],
      confidence: 0.88,
      rawText: `${merchant}\n123 Main St\nDate: ${new Date().toLocaleDateString()}\nTotal: $${amount}\nTax: $${tax}`
    };
  }

  _analyzeTopCategories(expenses) {
    const categoryTotals = {};
    expenses.forEach(expense => {
      const category = expense.category || 'Other';
      categoryTotals[category] = (categoryTotals[category] || 0) + parseFloat(expense.amount);
    });
    
    return Object.entries(categoryTotals)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 5)
      .map(([category, amount]) => ({ category, amount }));
  }

  _analyzeSpendingTrends(expenses) {
    const monthlySpending = {};
    expenses.forEach(expense => {
      const month = expense.expenseDate.substring(0, 7); // YYYY-MM
      monthlySpending[month] = (monthlySpending[month] || 0) + parseFloat(expense.amount);
    });
    
    const months = Object.keys(monthlySpending).sort();
    const trend = months.length > 1 ? 
      (monthlySpending[months[months.length - 1]] > monthlySpending[months[0]] ? 'increasing' : 'decreasing') :
      'stable';
    
    return { monthlySpending, trend };
  }

  _generateRecommendations(expenses) {
    const recommendations = [];
    const categoryTotals = this._analyzeTopCategories(expenses);
    
    if (categoryTotals.length > 0) {
      const topCategory = categoryTotals[0];
      if (topCategory.amount > 5000) {
        recommendations.push({
          type: 'cost_reduction',
          category: topCategory.category,
          message: `Consider reviewing ${topCategory.category} expenses - highest spending category`,
          priority: 'high'
        });
      }
    }
    
    return recommendations;
  }

  _detectAnomalies(expenses) {
    // Simple anomaly detection based on amount thresholds
    return expenses
      .filter(expense => parseFloat(expense.amount) > 1000)
      .map(expense => ({
        expenseId: expense.id,
        type: 'high_amount',
        amount: expense.amount,
        message: `Unusually high expense: $${expense.amount}`
      }));
  }

  _calculateCategoryAverages(expenses) {
    const categoryData = {};
    
    expenses.forEach(expense => {
      const category = expense.category || 'Other';
      if (!categoryData[category]) {
        categoryData[category] = { total: 0, count: 0 };
      }
      categoryData[category].total += parseFloat(expense.amount);
      categoryData[category].count += 1;
    });
    
    const averages = {};
    Object.keys(categoryData).forEach(category => {
      averages[category] = categoryData[category].total / categoryData[category].count;
    });
    
    return averages;
  }

  _analyzeCategorySpending(expenses) {
    const categorySpending = {};
    expenses.forEach(expense => {
      const category = expense.category || 'Other';
      categorySpending[category] = (categorySpending[category] || 0) + parseFloat(expense.amount);
    });
    return categorySpending;
  }

  _calculateMonthlyTrends(expenses) {
    const monthlyData = {};
    expenses.forEach(expense => {
      const month = expense.expenseDate.substring(0, 7);
      monthlyData[month] = (monthlyData[month] || 0) + parseFloat(expense.amount);
    });
    return monthlyData;
  }

  _generateProjections(trends, months) {
    const trendValues = Object.values(trends);
    const avgGrowth = trendValues.length > 1 ? 
      (trendValues[trendValues.length - 1] - trendValues[0]) / trendValues.length : 0;
    
    const projections = [];
    const lastValue = trendValues[trendValues.length - 1] || 0;
    
    for (let i = 1; i <= months; i++) {
      projections.push({
        month: i,
        projected: lastValue + (avgGrowth * i),
        confidence: Math.max(0.5, 0.9 - (i * 0.1))
      });
    }
    
    return projections;
  }

  _generateFinancialRecommendations(projections) {
    const recommendations = [];
    
    if (projections.some(p => p.projected > projections[0].projected * 1.2)) {
      recommendations.push({
        type: 'spending_control',
        message: 'Projected spending increase detected - consider budget review',
        priority: 'medium'
      });
    }
    
    return recommendations;
  }

  _identifyRiskFactors(projections) {
    const risks = [];
    
    const highGrowthMonths = projections.filter(p => p.projected > projections[0].projected * 1.5);
    if (highGrowthMonths.length > 0) {
      risks.push({
        type: 'rapid_growth',
        severity: 'high',
        message: 'Rapid spending growth projected'
      });
    }
    
    return risks;
  }
}

module.exports = new AiService();