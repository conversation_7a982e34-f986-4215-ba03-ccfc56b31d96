'use strict';

const aiService = require('../../../services/aiService');
const { Expense, Category } = require('../../../models');
const { ApiError } = require('../../../utils/apiError');
const logger = require('../../../utils/logger');

/**
 * Auto-categorize expense using AI
 */
const categorizeExpense = async (req, res, next) => {
  try {
    const { description, merchant, amount } = req.body;

    if (!description) {
      throw ApiError.badRequest('Description is required');
    }

    const result = await aiService.categorizeExpense({
      description,
      merchant,
      amount
    });

    res.status(200).json({
      status: 'success',
      data: result
    });
  } catch (error) {
    logger.error('Error categorizing expense:', error);
    next(error);
  }
};

/**
 * Extract receipt data using OCR
 */
const extractReceipt = async (req, res, next) => {
  try {
    if (!req.file) {
      throw ApiError.badRequest('Receipt image is required');
    }

    const result = await aiService.extractReceiptData(req.file.path);

    res.status(200).json({
      status: 'success',
      data: result
    });
  } catch (error) {
    logger.error('Error extracting receipt:', error);
    next(error);
  }
};

/**
 * Generate spending insights
 */
const getSpendingInsights = async (req, res, next) => {
  try {
    const companyId = req.user.companyId;
    const { months = 3 } = req.query;

    if (!companyId) {
      throw ApiError.badRequest('User must be associated with a company');
    }

    // Get recent expenses
    const startDate = new Date();
    startDate.setMonth(startDate.getMonth() - parseInt(months));

    const expenses = await Expense.findAll({
      where: {
        companyId,
        expenseDate: { [require('sequelize').Op.gte]: startDate },
        status: 'approved'
      },
      include: [{
        model: Category,
        as: 'category',
        attributes: ['name']
      }],
      attributes: ['id', 'amount', 'amountInBaseCurrency', 'expenseDate', 'description', 'merchant']
    });

    const expenseData = expenses.map(expense => ({
      id: expense.id,
      amount: expense.amountInBaseCurrency,
      category: expense.category?.name || 'Other',
      expenseDate: expense.expenseDate,
      description: expense.description,
      merchant: expense.merchant
    }));

    const insights = await aiService.generateSpendingInsights(expenseData);

    res.status(200).json({
      status: 'success',
      data: insights
    });
  } catch (error) {
    logger.error('Error generating spending insights:', error);
    next(error);
  }
};

/**
 * Detect spending anomalies
 */
const detectAnomalies = async (req, res, next) => {
  try {
    const companyId = req.user.companyId;
    const { months = 6 } = req.query;

    if (!companyId) {
      throw ApiError.badRequest('User must be associated with a company');
    }

    // Get historical expenses for anomaly detection
    const startDate = new Date();
    startDate.setMonth(startDate.getMonth() - parseInt(months));

    const expenses = await Expense.findAll({
      where: {
        companyId,
        expenseDate: { [require('sequelize').Op.gte]: startDate },
        status: 'approved'
      },
      include: [{
        model: Category,
        as: 'category',
        attributes: ['name']
      }],
      attributes: ['id', 'amount', 'amountInBaseCurrency', 'expenseDate', 'description', 'merchant']
    });

    const expenseData = expenses.map(expense => ({
      id: expense.id,
      amount: expense.amountInBaseCurrency,
      category: expense.category?.name || 'Other',
      expenseDate: expense.expenseDate,
      description: expense.description,
      merchant: expense.merchant
    }));

    const anomalies = await aiService.detectAnomalies(expenseData);

    res.status(200).json({
      status: 'success',
      data: {
        anomalies,
        totalExpenses: expenses.length,
        anomalyCount: anomalies.length,
        anomalyRate: expenses.length > 0 ? (anomalies.length / expenses.length * 100).toFixed(2) : 0
      }
    });
  } catch (error) {
    logger.error('Error detecting anomalies:', error);
    next(error);
  }
};

/**
 * Get AI-powered budget optimization suggestions
 */
const optimizeBudget = async (req, res, next) => {
  try {
    const companyId = req.user.companyId;
    const { budgetData } = req.body;

    if (!companyId) {
      throw ApiError.badRequest('User must be associated with a company');
    }

    if (!budgetData) {
      throw ApiError.badRequest('Budget data is required');
    }

    // Get historical expenses for analysis
    const startDate = new Date();
    startDate.setMonth(startDate.getMonth() - 6); // Last 6 months

    const expenses = await Expense.findAll({
      where: {
        companyId,
        expenseDate: { [require('sequelize').Op.gte]: startDate },
        status: 'approved'
      },
      include: [{
        model: Category,
        as: 'category',
        attributes: ['name']
      }],
      attributes: ['amountInBaseCurrency', 'expenseDate']
    });

    const expenseData = expenses.map(expense => ({
      amount: expense.amountInBaseCurrency,
      category: expense.category?.name || 'Other',
      expenseDate: expense.expenseDate
    }));

    const suggestions = await aiService.optimizeBudget(budgetData, expenseData);

    res.status(200).json({
      status: 'success',
      data: suggestions
    });
  } catch (error) {
    logger.error('Error optimizing budget:', error);
    next(error);
  }
};

/**
 * Generate predictive financial analysis
 */
const getPredictiveAnalysis = async (req, res, next) => {
  try {
    const companyId = req.user.companyId;

    if (!companyId) {
      throw ApiError.badRequest('User must be associated with a company');
    }

    // Get historical financial data
    const startDate = new Date();
    startDate.setFullYear(startDate.getFullYear() - 1); // Last year

    const expenses = await Expense.findAll({
      where: {
        companyId,
        expenseDate: { [require('sequelize').Op.gte]: startDate },
        status: 'approved'
      },
      attributes: ['amountInBaseCurrency', 'expenseDate']
    });

    const financialData = {
      expenses: expenses.map(expense => ({
        amount: expense.amountInBaseCurrency,
        date: expense.expenseDate
      })),
      revenue: [], // Would be populated from revenue data
      cashFlow: [] // Would be populated from cash flow data
    };

    const analysis = await aiService.generatePredictiveAnalysis(financialData);

    res.status(200).json({
      status: 'success',
      data: analysis
    });
  } catch (error) {
    logger.error('Error generating predictive analysis:', error);
    next(error);
  }
};

module.exports = {
  categorizeExpense,
  extractReceipt,
  getSpendingInsights,
  detectAnomalies,
  optimizeBudget,
  getPredictiveAnalysis
};