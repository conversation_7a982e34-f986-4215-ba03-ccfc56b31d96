'use strict';

const express = require('express');
const { authenticate } = require('../../../middleware/auth');
const upload = require('../../../middleware/upload');
const aiController = require('../controllers/aiController');

const router = express.Router();

// All routes require authentication
router.use(authenticate);

/**
 * @swagger
 * /api/v1/ai/categorize-expense:
 *   post:
 *     summary: Auto-categorize expense using AI
 *     tags: [AI Features]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - description
 *             properties:
 *               description:
 *                 type: string
 *               merchant:
 *                 type: string
 *               amount:
 *                 type: number
 *     responses:
 *       200:
 *         description: Expense categorized successfully
 */
router.post('/categorize-expense', aiController.categorizeExpense);

/**
 * @swagger
 * /api/v1/ai/extract-receipt:
 *   post:
 *     summary: Extract data from receipt using OCR
 *     tags: [AI Features]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         multipart/form-data:
 *           schema:
 *             type: object
 *             required:
 *               - receipt
 *             properties:
 *               receipt:
 *                 type: string
 *                 format: binary
 *     responses:
 *       200:
 *         description: Receipt data extracted successfully
 */
router.post('/extract-receipt', upload.single('receipt'), aiController.extractReceipt);

/**
 * @swagger
 * /api/v1/ai/spending-insights:
 *   get:
 *     summary: Get AI-generated spending insights
 *     tags: [AI Features]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: months
 *         schema:
 *           type: integer
 *           default: 3
 *         description: Number of months to analyze
 *     responses:
 *       200:
 *         description: Spending insights generated successfully
 */
router.get('/spending-insights', aiController.getSpendingInsights);

/**
 * @swagger
 * /api/v1/ai/anomaly-detection:
 *   get:
 *     summary: Detect unusual spending patterns
 *     tags: [AI Features]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: months
 *         schema:
 *           type: integer
 *           default: 6
 *         description: Number of months to analyze
 *     responses:
 *       200:
 *         description: Anomalies detected successfully
 */
router.get('/anomaly-detection', aiController.detectAnomalies);

/**
 * @swagger
 * /api/v1/ai/optimize-budget:
 *   post:
 *     summary: Get AI-powered budget optimization suggestions
 *     tags: [AI Features]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - budgetData
 *             properties:
 *               budgetData:
 *                 type: object
 *                 description: Current budget allocation by category
 *     responses:
 *       200:
 *         description: Budget optimization suggestions generated
 */
router.post('/optimize-budget', aiController.optimizeBudget);

/**
 * @swagger
 * /api/v1/ai/predictive-analysis:
 *   get:
 *     summary: Generate predictive financial analysis
 *     tags: [AI Features]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Predictive analysis generated successfully
 */
router.get('/predictive-analysis', aiController.getPredictiveAnalysis);

module.exports = router;