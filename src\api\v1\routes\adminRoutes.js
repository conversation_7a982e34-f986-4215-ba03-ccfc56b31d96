'use strict';

const express = require('express');
const { authenticate, authorize } = require('../../../middleware/auth');
const adminController = require('../controllers/adminController');

const router = express.Router();

// All routes require authentication and admin/manager role
router.use(authenticate);
router.use(authorize('admin', 'manager'));

/**
 * @swagger
 * /api/v1/admin/users:
 *   get:
 *     summary: Get all users with filtering and pagination
 *     tags: [Admin]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 20
 *       - in: query
 *         name: search
 *         schema:
 *           type: string
 *       - in: query
 *         name: role
 *         schema:
 *           type: string
 *       - in: query
 *         name: department
 *         schema:
 *           type: string
 *       - in: query
 *         name: status
 *         schema:
 *           type: string
 *           enum: [active, inactive]
 *     responses:
 *       200:
 *         description: Users retrieved successfully
 */
router.get('/users', adminController.getUsers);

/**
 * @swagger
 * /api/v1/admin/users/{userId}/deactivate:
 *   post:
 *     summary: Deactivate a user
 *     tags: [Admin]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: userId
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *     requestBody:
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               reason:
 *                 type: string
 *     responses:
 *       200:
 *         description: User deactivated successfully
 */
router.post('/users/:userId/deactivate', adminController.deactivateUser);

/**
 * @swagger
 * /api/v1/admin/users/{userId}/activate:
 *   post:
 *     summary: Activate a user
 *     tags: [Admin]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: userId
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *     responses:
 *       200:
 *         description: User activated successfully
 */
router.post('/users/:userId/activate', adminController.activateUser);

/**
 * @swagger
 * /api/v1/admin/system-stats:
 *   get:
 *     summary: Get system usage statistics
 *     tags: [Admin]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: timeframe
 *         schema:
 *           type: string
 *           enum: [7d, 30d, 90d]
 *           default: 30d
 *     responses:
 *       200:
 *         description: System stats retrieved successfully
 */
router.get('/system-stats', adminController.getSystemStats);

/**
 * @swagger
 * /api/v1/admin/bulk-operations:
 *   post:
 *     summary: Perform bulk operations on users
 *     tags: [Admin]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - operation
 *               - userIds
 *             properties:
 *               operation:
 *                 type: string
 *                 enum: [deactivate, activate, updateRole, updateDepartment]
 *               userIds:
 *                 type: array
 *                 items:
 *                   type: string
 *                   format: uuid
 *               data:
 *                 type: object
 *                 properties:
 *                   role:
 *                     type: string
 *                   departmentId:
 *                     type: string
 *                     format: uuid
 *     responses:
 *       200:
 *         description: Bulk operation completed
 */
router.post('/bulk-operations', adminController.bulkOperations);

/**
 * @swagger
 * /api/v1/admin/audit-trail:
 *   get:
 *     summary: Get complete system audit trail
 *     tags: [Admin]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 50
 *       - in: query
 *         name: entityType
 *         schema:
 *           type: string
 *       - in: query
 *         name: action
 *         schema:
 *           type: string
 *       - in: query
 *         name: userId
 *         schema:
 *           type: string
 *           format: uuid
 *       - in: query
 *         name: startDate
 *         schema:
 *           type: string
 *           format: date
 *       - in: query
 *         name: endDate
 *         schema:
 *           type: string
 *           format: date
 *     responses:
 *       200:
 *         description: Audit trail retrieved successfully
 */
router.get('/audit-trail', adminController.getAuditTrail);

module.exports = router;