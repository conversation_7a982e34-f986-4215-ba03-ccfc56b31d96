'use strict';

module.exports = (sequelize, DataTypes) => {
  const Approval = sequelize.define('Approval', {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true
    },
    expenseId: {
      type: DataTypes.UUID,
      allowNull: false,
      references: {
        model: 'Expenses',
        key: 'id'
      }
    },
    approverId: {
      type: DataTypes.UUID,
      allowNull: false,
      references: {
        model: 'Users',
        key: 'id'
      }
    },
    status: {
      type: DataTypes.ENUM('pending', 'approved', 'rejected', 'escalated'),
      defaultValue: 'pending'
    },
    comments: {
      type: DataTypes.TEXT
    },
    level: {
      type: DataTypes.INTEGER,
      defaultValue: 1
    },
    order: {
      type: DataTypes.INTEGER
    },
    actionDate: {
      type: DataTypes.DATE
    },
    reminderSent: {
      type: DataTypes.BOOLEAN,
      defaultValue: false
    },
    reminderCount: {
      type: DataTypes.INTEGER,
      defaultValue: 0
    },
    isEscalation: {
      type: DataTypes.BOOLEAN,
      defaultValue: false
    },
    escalatedFrom: {
      type: DataTypes.UUID,
      references: {
        model: 'Users',
        key: 'id'
      }
    }
  }, {
    timestamps: true,
    indexes: [
      { fields: ['expenseId'] },
      { fields: ['approverId'] },
      { fields: ['status'] }
    ]
  });

  Approval.associate = (models) => {
    Approval.belongsTo(models.Expense, {
      foreignKey: 'expenseId',
      as: 'expense'
    });
    
    Approval.belongsTo(models.User, {
      foreignKey: 'approverId',
      as: 'approver'
    });
    
    Approval.belongsTo(models.User, {
      foreignKey: 'escalatedFrom',
      as: 'previousApprover'
    });
  };

  return Approval;
};