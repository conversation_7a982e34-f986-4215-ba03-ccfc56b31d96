'use strict';

const { Notification } = require('../models');
const logger = require('../utils/logger');
const { ApiError } = require('../utils/apiError');

/**
 * Service for managing notifications
 */
class NotificationService {
  
  /**
   * Create a new notification
   * @param {Object} notificationData - Notification data
   * @returns {Promise<Object>} Created notification
   */
  async createNotification(notificationData) {
    try {
      const {
        userId,
        title,
        message,
        type,
        data = {},
        priority = 'medium'
      } = notificationData;
      
      const notification = await Notification.create({
        userId,
        title,
        message,
        type,
        data,
        priority
      });
      
      // Emit real-time notification if WebSocket is available
      this._emitRealTimeNotification(userId, notification);
      
      return notification;
    } catch (error) {
      logger.error('Error creating notification:', error);
      throw new ApiError('Failed to create notification', 500);
    }
  }
  
  /**
   * Get notifications for a user
   * @param {string} userId - User ID
   * @param {Object} options - Query options
   * @returns {Promise<Object>} Notifications with pagination
   */
  async getUserNotifications(userId, options = {}) {
    try {
      const {
        page = 1,
        limit = 20,
        type,
        isRead,
        priority
      } = options;
      
      const where = { userId };
      
      if (type) where.type = type;
      if (typeof isRead === 'boolean') where.isRead = isRead;
      if (priority) where.priority = priority;
      
      const offset = (page - 1) * limit;
      
      const { count, rows } = await Notification.findAndCountAll({
        where,
        order: [['createdAt', 'DESC']],
        limit: parseInt(limit),
        offset
      });
      
      return {
        notifications: rows,
        pagination: {
          total: count,
          page: parseInt(page),
          limit: parseInt(limit),
          totalPages: Math.ceil(count / limit)
        }
      };
    } catch (error) {
      logger.error('Error getting user notifications:', error);
      throw new ApiError('Failed to get notifications', 500);
    }
  }
  
  /**
   * Mark notification as read
   * @param {string} notificationId - Notification ID
   * @param {string} userId - User ID
   * @returns {Promise<Object>} Updated notification
   */
  async markAsRead(notificationId, userId) {
    try {
      const notification = await Notification.findOne({
        where: { id: notificationId, userId }
      });
      
      if (!notification) {
        throw new ApiError('Notification not found', 404);
      }
      
      notification.isRead = true;
      await notification.save();
      
      return notification;
    } catch (error) {
      logger.error('Error marking notification as read:', error);
      throw error instanceof ApiError ? error : new ApiError('Failed to mark notification as read', 500);
    }
  }
  
  /**
   * Mark all notifications as read for a user
   * @param {string} userId - User ID
   * @returns {Promise<number>} Number of updated notifications
   */
  async markAllAsRead(userId) {
    try {
      const [updatedCount] = await Notification.update(
        { isRead: true },
        { 
          where: { 
            userId, 
            isRead: false 
          } 
        }
      );
      
      return updatedCount;
    } catch (error) {
      logger.error('Error marking all notifications as read:', error);
      throw new ApiError('Failed to mark all notifications as read', 500);
    }
  }
  
  /**
   * Delete a notification
   * @param {string} notificationId - Notification ID
   * @param {string} userId - User ID
   * @returns {Promise<boolean>} Success status
   */
  async deleteNotification(notificationId, userId) {
    try {
      const deleted = await Notification.destroy({
        where: { id: notificationId, userId }
      });
      
      if (!deleted) {
        throw new ApiError('Notification not found', 404);
      }
      
      return true;
    } catch (error) {
      logger.error('Error deleting notification:', error);
      throw error instanceof ApiError ? error : new ApiError('Failed to delete notification', 500);
    }
  }
  
  /**
   * Get notification statistics for a user
   * @param {string} userId - User ID
   * @returns {Promise<Object>} Notification statistics
   */
  async getNotificationStats(userId) {
    try {
      const [total, unread, byType, byPriority] = await Promise.all([
        Notification.count({ where: { userId } }),
        Notification.count({ where: { userId, isRead: false } }),
        Notification.findAll({
          where: { userId },
          attributes: [
            'type',
            [Notification.sequelize.fn('COUNT', '*'), 'count']
          ],
          group: ['type'],
          raw: true
        }),
        Notification.findAll({
          where: { userId, isRead: false },
          attributes: [
            'priority',
            [Notification.sequelize.fn('COUNT', '*'), 'count']
          ],
          group: ['priority'],
          raw: true
        })
      ]);
      
      return {
        total,
        unread,
        byType: byType.reduce((acc, item) => {
          acc[item.type] = parseInt(item.count);
          return acc;
        }, {}),
        byPriority: byPriority.reduce((acc, item) => {
          acc[item.priority] = parseInt(item.count);
          return acc;
        }, {})
      };
    } catch (error) {
      logger.error('Error getting notification stats:', error);
      throw new ApiError('Failed to get notification statistics', 500);
    }
  }
  
  /**
   * Create expense-related notifications
   * @param {Object} expense - Expense object
   * @param {string} action - Action type
   * @returns {Promise<void>}
   */
  async createExpenseNotification(expense, action) {
    try {
      const notifications = [];
      
      switch (action) {
        case 'submitted':
          // Notify approvers
          if (expense.approvals && expense.approvals.length > 0) {
            for (const approval of expense.approvals) {
              notifications.push({
                userId: approval.approverId,
                title: 'New Expense Approval Required',
                message: `${expense.user.firstName} ${expense.user.lastName} submitted an expense for $${expense.amount}`,
                type: 'approval_required',
                priority: expense.amount > 1000 ? 'high' : 'medium',
                data: {
                  expenseId: expense.id,
                  amount: expense.amount,
                  submittedBy: expense.userId
                }
              });
            }
          }
          break;
          
        case 'approved':
          // Notify expense submitter
          notifications.push({
            userId: expense.userId,
            title: 'Expense Approved',
            message: `Your expense for $${expense.amount} has been approved`,
            type: 'expense_approved',
            priority: 'medium',
            data: {
              expenseId: expense.id,
              amount: expense.amount
            }
          });
          break;
          
        case 'rejected':
          // Notify expense submitter
          notifications.push({
            userId: expense.userId,
            title: 'Expense Rejected',
            message: `Your expense for $${expense.amount} has been rejected`,
            type: 'expense_rejected',
            priority: 'high',
            data: {
              expenseId: expense.id,
              amount: expense.amount
            }
          });
          break;
      }
      
      // Create all notifications
      for (const notificationData of notifications) {
        await this.createNotification(notificationData);
      }
    } catch (error) {
      logger.error('Error creating expense notification:', error);
      // Don't throw error to avoid breaking the main flow
    }
  }
  
  /**
   * Create budget alert notifications
   * @param {Object} alert - Budget alert data
   * @returns {Promise<void>}
   */
  async createBudgetAlertNotification(alert) {
    try {
      const { User } = require('../models');
      
      // Get users who should receive budget alerts (managers, admins)
      const users = await User.findAll({
        where: {
          companyId: alert.companyId,
          role: ['admin', 'manager']
        }
      });
      
      for (const user of users) {
        await this.createNotification({
          userId: user.id,
          title: 'Budget Alert',
          message: alert.message,
          type: 'budget_alert',
          priority: alert.severity === 'critical' ? 'urgent' : 'high',
          data: {
            alertType: alert.alertType,
            threshold: alert.thresholdValue,
            current: alert.currentValue
          }
        });
      }
    } catch (error) {
      logger.error('Error creating budget alert notification:', error);
    }
  }
  
  /**
   * Emit real-time notification via WebSocket
   * @param {string} userId - User ID
   * @param {Object} notification - Notification data
   * @private
   */
  _emitRealTimeNotification(userId, notification) {
    try {
      // This would integrate with your WebSocket implementation
      // For now, just log the event
      logger.info(`Real-time notification for user ${userId}:`, {
        id: notification.id,
        title: notification.title,
        type: notification.type,
        priority: notification.priority
      });
      
      // In a real implementation, you would emit to WebSocket:
      // io.to(`user_${userId}`).emit('notification', notification);
    } catch (error) {
      logger.error('Error emitting real-time notification:', error);
    }
  }
}

module.exports = new NotificationService();