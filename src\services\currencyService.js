'use strict';

const axios = require('axios');
const { ExchangeRate } = require('../models');
const logger = require('../utils/logger');
const { ApiError } = require('../utils/apiError');
const { Op } = require('sequelize');

/**
 * Service for currency conversion and exchange rates
 */
class CurrencyService {
  constructor() {
    this.apiKey = process.env.EXCHANGE_RATE_API_KEY;
    this.apiUrl = 'https://api.exchangerate-api.com/v4/latest';
    this.supportedCurrencies = [
      'USD', 'EUR', 'GBP', 'JPY', 'CAD', 'AUD', 'CHF', 'CNY', 'INR', 'BRL'
    ];
  }

  /**
   * Get current exchange rates
   * @param {string} baseCurrency - Base currency code
   * @returns {Promise<Object>} Exchange rates
   */
  async getCurrentRates(baseCurrency = 'USD') {
    try {
      // Try to get from cache first
      const cachedRates = await this._getCachedRates(baseCurrency);
      if (cachedRates) {
        return cachedRates;
      }

      // Fetch from API (simulated for development)
      const rates = await this._fetchRatesFromAPI(baseCurrency);
      
      // Cache the rates
      await this._cacheRates(baseCurrency, rates);
      
      return {
        base: baseCurrency,
        rates,
        lastUpdated: new Date()
      };
    } catch (error) {
      logger.error('Error getting current rates:', error);
      throw new ApiError('Failed to get exchange rates', 500);
    }
  }

  /**
   * Convert amount between currencies
   * @param {number} amount - Amount to convert
   * @param {string} fromCurrency - Source currency
   * @param {string} toCurrency - Target currency
   * @param {Date} date - Date for historical rates (optional)
   * @returns {Promise<Object>} Conversion result
   */
  async convertCurrency(amount, fromCurrency, toCurrency, date = null) {
    try {
      if (fromCurrency === toCurrency) {
        return {
          amount,
          convertedAmount: amount,
          rate: 1,
          fromCurrency,
          toCurrency,
          date: date || new Date()
        };
      }

      const rate = await this._getExchangeRate(fromCurrency, toCurrency, date);
      const convertedAmount = amount * rate;

      return {
        amount,
        convertedAmount: Math.round(convertedAmount * 100) / 100,
        rate,
        fromCurrency,
        toCurrency,
        date: date || new Date()
      };
    } catch (error) {
      logger.error('Error converting currency:', error);
      throw new ApiError('Failed to convert currency', 500);
    }
  }

  /**
   * Get supported currencies
   * @returns {Promise<Array>} List of supported currencies
   */
  async getSupportedCurrencies() {
    try {
      return this.supportedCurrencies.map(code => ({
        code,
        name: this._getCurrencyName(code),
        symbol: this._getCurrencySymbol(code)
      }));
    } catch (error) {
      logger.error('Error getting supported currencies:', error);
      throw new ApiError('Failed to get supported currencies', 500);
    }
  }

  /**
   * Get historical exchange rates
   * @param {string} fromCurrency - Source currency
   * @param {string} toCurrency - Target currency
   * @param {Date} startDate - Start date
   * @param {Date} endDate - End date
   * @returns {Promise<Array>} Historical rates
   */
  async getHistoricalRates(fromCurrency, toCurrency, startDate, endDate) {
    try {
      const rates = await ExchangeRate.findAll({
        where: {
          fromCurrency,
          toCurrency,
          date: {
            [Op.between]: [startDate, endDate]
          }
        },
        order: [['date', 'ASC']]
      });

      return rates.map(rate => ({
        date: rate.date,
        rate: parseFloat(rate.rate),
        source: rate.source
      }));
    } catch (error) {
      logger.error('Error getting historical rates:', error);
      throw new ApiError('Failed to get historical rates', 500);
    }
  }

  /**
   * Update exchange rates from external API
   * @returns {Promise<Object>} Update result
   */
  async updateExchangeRates() {
    try {
      let updated = 0;
      let errors = 0;

      for (const baseCurrency of ['USD', 'EUR', 'GBP']) {
        try {
          const rates = await this._fetchRatesFromAPI(baseCurrency);
          await this._cacheRates(baseCurrency, rates);
          updated++;
        } catch (error) {
          logger.error(`Error updating rates for ${baseCurrency}:`, error);
          errors++;
        }
      }

      return { updated, errors };
    } catch (error) {
      logger.error('Error updating exchange rates:', error);
      throw new ApiError('Failed to update exchange rates', 500);
    }
  }

  // Private methods

  /**
   * Get cached exchange rates
   * @param {string} baseCurrency - Base currency
   * @returns {Promise<Object|null>} Cached rates or null
   * @private
   */
  async _getCachedRates(baseCurrency) {
    try {
      const today = new Date().toISOString().split('T')[0];
      
      const rates = await ExchangeRate.findAll({
        where: {
          fromCurrency: baseCurrency,
          date: today
        }
      });

      if (rates.length === 0) {
        return null;
      }

      const rateMap = {};
      rates.forEach(rate => {
        rateMap[rate.toCurrency] = parseFloat(rate.rate);
      });

      return rateMap;
    } catch (error) {
      logger.error('Error getting cached rates:', error);
      return null;
    }
  }

  /**
   * Fetch rates from external API
   * @param {string} baseCurrency - Base currency
   * @returns {Promise<Object>} Exchange rates
   * @private
   */
  async _fetchRatesFromAPI(baseCurrency) {
    try {
      // Simulate API response for development
      const simulatedRates = this._generateSimulatedRates(baseCurrency);
      return simulatedRates;
    } catch (error) {
      logger.error('Error fetching rates from API:', error);
      throw error;
    }
  }

  /**
   * Cache exchange rates in database
   * @param {string} baseCurrency - Base currency
   * @param {Object} rates - Exchange rates
   * @returns {Promise<void>}
   * @private
   */
  async _cacheRates(baseCurrency, rates) {
    try {
      const today = new Date().toISOString().split('T')[0];
      
      for (const [toCurrency, rate] of Object.entries(rates)) {
        await ExchangeRate.upsert({
          fromCurrency: baseCurrency,
          toCurrency,
          rate,
          date: today,
          source: 'api'
        });
      }
    } catch (error) {
      logger.error('Error caching rates:', error);
      throw error;
    }
  }

  /**
   * Get exchange rate between two currencies
   * @param {string} fromCurrency - Source currency
   * @param {string} toCurrency - Target currency
   * @param {Date} date - Date for rate (optional)
   * @returns {Promise<number>} Exchange rate
   * @private
   */
  async _getExchangeRate(fromCurrency, toCurrency, date = null) {
    try {
      const queryDate = date ? date.toISOString().split('T')[0] : new Date().toISOString().split('T')[0];
      
      // Try direct rate
      let rate = await ExchangeRate.findOne({
        where: {
          fromCurrency,
          toCurrency,
          date: queryDate
        }
      });

      if (rate) {
        return parseFloat(rate.rate);
      }

      // Try inverse rate
      rate = await ExchangeRate.findOne({
        where: {
          fromCurrency: toCurrency,
          toCurrency: fromCurrency,
          date: queryDate
        }
      });

      if (rate) {
        return 1 / parseFloat(rate.rate);
      }

      // If no rate found, fetch current rates
      if (!date) {
        const currentRates = await this.getCurrentRates(fromCurrency);
        return currentRates.rates[toCurrency] || 1;
      }

      throw new ApiError(`Exchange rate not found for ${fromCurrency} to ${toCurrency}`, 404);
    } catch (error) {
      logger.error('Error getting exchange rate:', error);
      throw error instanceof ApiError ? error : new ApiError('Failed to get exchange rate', 500);
    }
  }

  /**
   * Generate simulated exchange rates for development
   * @param {string} baseCurrency - Base currency
   * @returns {Object} Simulated rates
   * @private
   */
  _generateSimulatedRates(baseCurrency) {
    const baseRates = {
      USD: {
        EUR: 0.85,
        GBP: 0.73,
        JPY: 110.0,
        CAD: 1.25,
        AUD: 1.35,
        CHF: 0.92,
        CNY: 6.45,
        INR: 74.5,
        BRL: 5.2
      },
      EUR: {
        USD: 1.18,
        GBP: 0.86,
        JPY: 129.5,
        CAD: 1.47,
        AUD: 1.59,
        CHF: 1.08,
        CNY: 7.6,
        INR: 87.8,
        BRL: 6.1
      }
    };

    // Add some random variation (±2%)
    const rates = baseRates[baseCurrency] || baseRates.USD;
    const variatedRates = {};

    Object.entries(rates).forEach(([currency, rate]) => {
      const variation = (Math.random() - 0.5) * 0.04; // ±2%
      variatedRates[currency] = rate * (1 + variation);
    });

    return variatedRates;
  }

  /**
   * Get currency name
   * @param {string} code - Currency code
   * @returns {string} Currency name
   * @private
   */
  _getCurrencyName(code) {
    const names = {
      USD: 'US Dollar',
      EUR: 'Euro',
      GBP: 'British Pound',
      JPY: 'Japanese Yen',
      CAD: 'Canadian Dollar',
      AUD: 'Australian Dollar',
      CHF: 'Swiss Franc',
      CNY: 'Chinese Yuan',
      INR: 'Indian Rupee',
      BRL: 'Brazilian Real'
    };
    return names[code] || code;
  }

  /**
   * Get currency symbol
   * @param {string} code - Currency code
   * @returns {string} Currency symbol
   * @private
   */
  _getCurrencySymbol(code) {
    const symbols = {
      USD: '$',
      EUR: '€',
      GBP: '£',
      JPY: '¥',
      CAD: 'C$',
      AUD: 'A$',
      CHF: 'CHF',
      CNY: '¥',
      INR: '₹',
      BRL: 'R$'
    };
    return symbols[code] || code;
  }
}

module.exports = new CurrencyService();