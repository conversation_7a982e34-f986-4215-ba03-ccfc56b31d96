const { Pool } = require('pg');
const logger = require('../utils/logger');

const pool = new Pool({
    host: process.env.DB_HOST || 'localhost',
    port: process.env.DB_PORT || 5432,
    database: process.env.DB_NAME || 'finance_manager',
    user: process.env.DB_USER || 'postgres',
    password: process.env.DB_PASSWORD || 'password',
    max: 20,
    idleTimeoutMillis: 30000,
    connectionTimeoutMillis: 2000,
});

const connectDB = async () => {
    try {
        const client = await pool.connect();
        await client.query('SELECT NOW()');
        client.release();
        logger.info('Database connected successfully');
        return pool;
    } catch (error) {
        logger.error('Database connection failed:', error);
        throw error;
    }
};

// Database migration function
const runMigrations = async () => {
    const client = await pool.connect();
    try {
        await client.query('BEGIN');
        
        // Create users table
        await client.query(`
            CREATE TABLE IF NOT EXISTS users (
                id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
                email VARCHAR(255) UNIQUE NOT NULL,
                password_hash VARCHAR(255) NOT NULL,
                subscription_tier VARCHAR(20) DEFAULT 'free' CHECK (subscription_tier IN ('free', 'premium')),
                subscription_expires_at TIMESTAMP,
                refresh_token TEXT,
                created_at TIMESTAMP DEFAULT NOW(),
                updated_at TIMESTAMP DEFAULT NOW()
            )
        `);

        // Create accounts table
        await client.query(`
            CREATE TABLE IF NOT EXISTS accounts (
                id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
                user_id UUID REFERENCES users(id) ON DELETE CASCADE,
                name VARCHAR(255) NOT NULL,
                type VARCHAR(20) CHECK (type IN ('checking', 'savings', 'credit', 'investment', 'cash')),
                balance DECIMAL(12,2) DEFAULT 0,
                currency VARCHAR(3) DEFAULT 'USD',
                is_active BOOLEAN DEFAULT true,
                created_at TIMESTAMP DEFAULT NOW()
            )
        `);

        // Create categories table
        await client.query(`
            CREATE TABLE IF NOT EXISTS categories (
                id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
                user_id UUID REFERENCES users(id) ON DELETE CASCADE,
                name VARCHAR(255) NOT NULL,
                parent_id UUID REFERENCES categories(id),
                color VARCHAR(7),
                icon VARCHAR(50),
                is_default BOOLEAN DEFAULT false,
                is_active BOOLEAN DEFAULT true,
                created_at TIMESTAMP DEFAULT NOW()
            )
        `);

        // Create transactions table
        await client.query(`
            CREATE TABLE IF NOT EXISTS transactions (
                id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
                user_id UUID REFERENCES users(id) ON DELETE CASCADE,
                account_id UUID REFERENCES accounts(id) ON DELETE CASCADE,
                category_id UUID REFERENCES categories(id),
                amount DECIMAL(12,2) NOT NULL,
                type VARCHAR(20) CHECK (type IN ('income', 'expense', 'transfer')),
                description TEXT,
                transaction_date DATE NOT NULL,
                created_at TIMESTAMP DEFAULT NOW(),
                tags TEXT[],
                merchant VARCHAR(255),
                location VARCHAR(255),
                receipt_url VARCHAR(500),
                import_id UUID,
                original_description TEXT
            )
        `);

        // Create budgets table
        await client.query(`
            CREATE TABLE IF NOT EXISTS budgets (
                id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
                user_id UUID REFERENCES users(id) ON DELETE CASCADE,
                category_id UUID REFERENCES categories(id),
                amount DECIMAL(12,2) NOT NULL,
                period VARCHAR(20) CHECK (period IN ('weekly', 'monthly', 'yearly')),
                start_date DATE NOT NULL,
                end_date DATE NOT NULL,
                alert_threshold DECIMAL(3,2) DEFAULT 0.8,
                is_active BOOLEAN DEFAULT true,
                created_at TIMESTAMP DEFAULT NOW()
            )
        `);

        // Create bill_reminders table
        await client.query(`
            CREATE TABLE IF NOT EXISTS bill_reminders (
                id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
                user_id UUID REFERENCES users(id) ON DELETE CASCADE,
                title VARCHAR(255) NOT NULL,
                amount DECIMAL(12,2),
                due_date DATE NOT NULL,
                frequency VARCHAR(20) CHECK (frequency IN ('once', 'weekly', 'monthly', 'yearly')),
                category_id UUID REFERENCES categories(id),
                is_paid BOOLEAN DEFAULT false,
                reminder_days_before INTEGER DEFAULT 3,
                created_at TIMESTAMP DEFAULT NOW()
            )
        `);

        // Create account_shares table
        await client.query(`
            CREATE TABLE IF NOT EXISTS account_shares (
                id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
                owner_id UUID REFERENCES users(id) ON DELETE CASCADE,
                shared_with_id UUID REFERENCES users(id) ON DELETE CASCADE,
                account_id UUID REFERENCES accounts(id) ON DELETE CASCADE,
                permission_level VARCHAR(10) DEFAULT 'view' CHECK (permission_level IN ('view', 'edit')),
                shared_at TIMESTAMP DEFAULT NOW()
            )
        `);

        // NEW: Create statement_uploads table
        await client.query(`
            CREATE TABLE IF NOT EXISTS statement_uploads (
                id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
                user_id UUID REFERENCES users(id) ON DELETE CASCADE,
                filename VARCHAR(255) NOT NULL,
                original_filename VARCHAR(255) NOT NULL,
                file_type VARCHAR(50) NOT NULL,
                file_size INTEGER NOT NULL,
                s3_key VARCHAR(500),
                status VARCHAR(50) DEFAULT 'uploaded' CHECK (status IN ('uploaded', 'processing', 'parsed', 'categorized', 'ready', 'imported', 'failed')),
                raw_data JSONB,
                processed_data JSONB,
                error_message TEXT,
                total_transactions INTEGER DEFAULT 0,
                created_at TIMESTAMP DEFAULT NOW(),
                updated_at TIMESTAMP DEFAULT NOW()
            )
        `);

        // NEW: Create import_sessions table
        await client.query(`
            CREATE TABLE IF NOT EXISTS import_sessions (
                id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
                upload_id UUID REFERENCES statement_uploads(id) ON DELETE CASCADE,
                user_id UUID REFERENCES users(id) ON DELETE CASCADE,
                account_id UUID REFERENCES accounts(id) ON DELETE CASCADE,
                total_transactions INTEGER NOT NULL,
                imported_count INTEGER DEFAULT 0,
                skipped_count INTEGER DEFAULT 0,
                duplicate_count INTEGER DEFAULT 0,
                status VARCHAR(50) DEFAULT 'pending' CHECK (status IN ('pending', 'processing', 'completed', 'failed')),
                settings JSONB,
                created_at TIMESTAMP DEFAULT NOW(),
                completed_at TIMESTAMP
            )
        `);

        // NEW: Create ai_categorization_cache table
        await client.query(`
            CREATE TABLE IF NOT EXISTS ai_categorization_cache (
                id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
                description_hash VARCHAR(64) UNIQUE NOT NULL,
                original_description TEXT NOT NULL,
                cleaned_description VARCHAR(100) NOT NULL,
                suggested_category VARCHAR(255),
                confidence_score DECIMAL(3,2),
                merchant VARCHAR(255),
                created_at TIMESTAMP DEFAULT NOW(),
                last_used TIMESTAMP DEFAULT NOW(),
                usage_count INTEGER DEFAULT 1
            )
        `);

        // Create indexes for performance
        await client.query('CREATE INDEX IF NOT EXISTS idx_transactions_user_id ON transactions(user_id)');
        await client.query('CREATE INDEX IF NOT EXISTS idx_transactions_date ON transactions(transaction_date)');
        await client.query('CREATE INDEX IF NOT EXISTS idx_transactions_category ON transactions(category_id)');
        await client.query('CREATE INDEX IF NOT EXISTS idx_transactions_import_id ON transactions(import_id)');
        await client.query('CREATE INDEX IF NOT EXISTS idx_accounts_user_id ON accounts(user_id)');
        await client.query('CREATE INDEX IF NOT EXISTS idx_budgets_user_id ON budgets(user_id)');
        await client.query('CREATE INDEX IF NOT EXISTS idx_statement_uploads_user_id ON statement_uploads(user_id)');
        await client.query('CREATE INDEX IF NOT EXISTS idx_statement_uploads_status ON statement_uploads(status)');
        await client.query('CREATE INDEX IF NOT EXISTS idx_import_sessions_user_id ON import_sessions(user_id)');
        await client.query('CREATE INDEX IF NOT EXISTS idx_ai_cache_hash ON ai_categorization_cache(description_hash)');

        await client.query('COMMIT');
        logger.info('Database migrations completed successfully');
    } catch (error) {
        await client.query('ROLLBACK');
        logger.error('Migration failed:', error);
        throw error;
    } finally {
        client.release();
    }
};

module.exports = {
    pool,
    connectDB,
    runMigrations
};