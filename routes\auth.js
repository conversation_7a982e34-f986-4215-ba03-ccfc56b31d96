const express = require('express');
const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');
const { body } = require('express-validator');
const { pool } = require('../config/database');
const { handleValidationErrors } = require('../middleware/validation');
const logger = require('../utils/logger');

const router = express.Router();

// Registration
router.post('/register', [
    body('email').isEmail().normalizeEmail(),
    body('password').isLength({ min: 8 }).withMessage('Password must be at least 8 characters'),
    handleValidationErrors
], async (req, res, next) => {
    try {
        const { email, password } = req.body;

        // Check if user already exists
        const existingUser = await pool.query('SELECT id FROM users WHERE email = $1', [email]);
        if (existingUser.rows.length > 0) {
            return res.status(409).json({ error: 'User already exists' });
        }

        // Hash password
        const saltRounds = 12;
        const hashedPassword = await bcrypt.hash(password, saltRounds);

        // Create user
        const result = await pool.query(
            'INSERT INTO users (email, password_hash) VALUES ($1, $2) RETURNING id, email, subscription_tier, created_at',
            [email, hashedPassword]
        );

        const user = result.rows[0];

        // Generate tokens
        const accessToken = jwt.sign(
            { userId: user.id, email: user.email },
            process.env.JWT_SECRET,
            { expiresIn: '15m' }
        );

        const refreshToken = jwt.sign(
            { userId: user.id },
            process.env.JWT_REFRESH_SECRET,
            { expiresIn: '7d' }
        );

        // Store refresh token
        await pool.query(
            'UPDATE users SET refresh_token = $1 WHERE id = $2',
            [refreshToken, user.id]
        );

        // Create default categories
        await createDefaultCategories(user.id);

        logger.info(`User registered: ${email}`);

        res.status(201).json({
            message: 'User created successfully',
            user: {
                id: user.id,
                email: user.email,
                subscription_tier: user.subscription_tier
            },
            tokens: {
                access_token: accessToken,
                refresh_token: refreshToken
            }
        });
    } catch (error) {
        next(error);
    }
});

// Login
router.post('/login', [
    body('email').isEmail().normalizeEmail(),
    body('password').notEmpty(),
    handleValidationErrors
], async (req, res, next) => {
    try {
        const { email, password } = req.body;

        // Find user
        const result = await pool.query(
            'SELECT id, email, password_hash, subscription_tier, subscription_expires_at FROM users WHERE email = $1',
            [email]
        );

        if (result.rows.length === 0) {
            return res.status(401).json({ error: 'Invalid credentials' });
        }

        const user = result.rows[0];

        // Verify password
        const isPasswordValid = await bcrypt.compare(password, user.password_hash);
        if (!isPasswordValid) {
            return res.status(401).json({ error: 'Invalid credentials' });
        }

        // Generate tokens
        const accessToken = jwt.sign(
            { userId: user.id, email: user.email },
            process.env.JWT_SECRET,
            { expiresIn: '15m' }
        );

        const refreshToken = jwt.sign(
            { userId: user.id },
            process.env.JWT_REFRESH_SECRET,
            { expiresIn: '7d' }
        );

        // Store refresh token
        await pool.query(
            'UPDATE users SET refresh_token = $1 WHERE id = $2',
            [refreshToken, user.id]
        );

        logger.info(`User logged in: ${email}`);

        res.json({
            message: 'Login successful',
            user: {
                id: user.id,
                email: user.email,
                subscription_tier: user.subscription_tier,
                subscription_expires_at: user.subscription_expires_at
            },
            tokens: {
                access_token: accessToken,
                refresh_token: refreshToken
            }
        });
    } catch (error) {
        next(error);
    }
});

// Refresh token
router.post('/refresh', async (req, res, next) => {
    try {
        const { refresh_token } = req.body;

        if (!refresh_token) {
            return res.status(401).json({ error: 'Refresh token required' });
        }

        const decoded = jwt.verify(refresh_token, process.env.JWT_REFRESH_SECRET);
        
        // Verify refresh token in database
        const result = await pool.query(
            'SELECT id, email, refresh_token FROM users WHERE id = $1',
            [decoded.userId]
        );

        if (result.rows.length === 0 || result.rows[0].refresh_token !== refresh_token) {
            return res.status(401).json({ error: 'Invalid refresh token' });
        }

        const user = result.rows[0];

        // Generate new access token
        const accessToken = jwt.sign(
            { userId: user.id, email: user.email },
            process.env.JWT_SECRET,
            { expiresIn: '15m' }
        );

        res.json({
            access_token: accessToken
        });
    } catch (error) {
        if (error.name === 'JsonWebTokenError' || error.name === 'TokenExpiredError') {
            return res.status(401).json({ error: 'Invalid refresh token' });
        }
        next(error);
    }
});

// Logout
router.post('/logout', async (req, res, next) => {
    try {
        const { refresh_token } = req.body;

        if (refresh_token) {
            const decoded = jwt.verify(refresh_token, process.env.JWT_REFRESH_SECRET);
            await pool.query(
                'UPDATE users SET refresh_token = NULL WHERE id = $1',
                [decoded.userId]
            );
        }

        res.json({ message: 'Logged out successfully' });
    } catch (error) {
        // Even if token verification fails, we still want to return success
        res.json({ message: 'Logged out successfully' });
    }
});

// Helper function to create default categories
async function createDefaultCategories(userId) {
    const defaultCategories = [
        { name: 'Food & Dining', color: '#FF6B6B', icon: 'utensils' },
        { name: 'Transportation', color: '#4ECDC4', icon: 'car' },
        { name: 'Shopping', color: '#45B7D1', icon: 'shopping-bag' },
        { name: 'Entertainment', color: '#96CEB4', icon: 'film' },
        { name: 'Bills & Utilities', color: '#FFEAA7', icon: 'file-text' },
        { name: 'Healthcare', color: '#DDA0DD', icon: 'heart' },
        { name: 'Income', color: '#98D8C8', icon: 'dollar-sign' },
        { name: 'Savings', color: '#F7DC6F', icon: 'piggy-bank' }
    ];

    for (const category of defaultCategories) {
        await pool.query(
            'INSERT INTO categories (user_id, name, color, icon, is_default) VALUES ($1, $2, $3, $4, true)',
            [userId, category.name, category.color, category.icon]
        );
    }
}

module.exports = router;