const ExcelJS = require('exceljs');
const { pool } = require('../config/database');
const logger = require('../utils/logger');

class ExportService {
    async exportTransactionsToExcel(userId, filters = {}) {
        try {
            const transactions = await this.getFilteredTransactions(userId, filters);
            const workbook = new ExcelJS.Workbook();
            
            // Main transactions sheet
            const worksheet = workbook.addWorksheet('Transactions');
            
            // Define columns with styling
            worksheet.columns = [
                { header: 'Date', key: 'date', width: 12 },
                { header: 'Description', key: 'description', width: 30 },
                { header: 'Category', key: 'category', width: 15 },
                { header: 'Amount', key: 'amount', width: 12 },
                { header: 'Type', key: 'type', width: 10 },
                { header: 'Account', key: 'account', width: 15 },
                { header: 'Merchant', key: 'merchant', width: 20 },
                { header: 'Location', key: 'location', width: 20 },
                { header: 'Tags', key: 'tags', width: 25 }
            ];

            // Style the header row
            worksheet.getRow(1).font = { bold: true };
            worksheet.getRow(1).fill = {
                type: 'pattern',
                pattern: 'solid',
                fgColor: { argb: 'FFE0E0E0' }
            };

            // Add data with conditional formatting
            transactions.forEach(transaction => {
                const row = worksheet.addRow({
                    date: transaction.transaction_date,
                    description: transaction.description,
                    category: transaction.category_name,
                    amount: parseFloat(transaction.amount),
                    type: transaction.type,
                    account: transaction.account_name,
                    merchant: transaction.merchant,
                    location: transaction.location,
                    tags: transaction.tags ? transaction.tags.join(', ') : ''
                });

                // Color code based on type
                if (transaction.type === 'expense') {
                    row.getCell('amount').font = { color: { argb: 'FFFF0000' } };
                    row.getCell('amount').numFmt = '-$#,##0.00';
                } else if (transaction.type === 'income') {
                    row.getCell('amount').font = { color: { argb: 'FF00AA00' } };
                    row.getCell('amount').numFmt = '$#,##0.00';
                }

                // Format date
                row.getCell('date').numFmt = 'mm/dd/yyyy';
            });

            // Add summary sheet
            await this.addSummarySheet(workbook, userId, filters);

            // Add charts sheet
            await this.addChartsSheet(workbook, transactions);

            return workbook.xlsx.writeBuffer();
        } catch (error) {
            logger.error('Failed to export transactions to Excel:', error);
            throw error;
        }
    }

    async getFilteredTransactions(userId, filters) {
        let query = `
            SELECT 
                t.*,
                c.name as category_name,
                a.name as account_name
            FROM transactions t
            LEFT JOIN categories c ON t.category_id = c.id
            LEFT JOIN accounts a ON t.account_id = a.id
            WHERE t.user_id = $1
        `;
        
        const params = [userId];
        let paramIndex = 2;

        if (filters.start_date && filters.end_date) {
            query += ` AND t.transaction_date BETWEEN $${paramIndex} AND $${paramIndex + 1}`;
            params.push(filters.start_date, filters.end_date);
            paramIndex += 2;
        }

        if (filters.category_id) {
            query += ` AND t.category_id = $${paramIndex}`;
            params.push(filters.category_id);
            paramIndex++;
        }

        if (filters.account_id) {
            query += ` AND t.account_id = $${paramIndex}`;
            params.push(filters.account_id);
            paramIndex++;
        }

        if (filters.type) {
            query += ` AND t.type = $${paramIndex}`;
            params.push(filters.type);
            paramIndex++;
        }

        if (filters.min_amount !== undefined) {
            query += ` AND t.amount >= $${paramIndex}`;
            params.push(filters.min_amount);
            paramIndex++;
        }

        if (filters.max_amount !== undefined) {
            query += ` AND t.amount <= $${paramIndex}`;
            params.push(filters.max_amount);
            paramIndex++;
        }

        query += ' ORDER BY t.transaction_date DESC';

        const result = await pool.query(query, params);
        return result.rows;
    }

    async addSummarySheet(workbook, userId, filters) {
        const summarySheet = workbook.addWorksheet('Summary');
        
        // Get summary data
        const summaryData = await this.getSummaryData(userId, filters);
        
        // Add summary information
        summarySheet.mergeCells('A1:B1');
        summarySheet.getCell('A1').value = 'Transaction Summary Report';
        summarySheet.getCell('A1').font = { bold: true, size: 16 };
        
        let row = 3;
        summarySheet.getCell(`A${row}`).value = 'Total Income:';
        summarySheet.getCell(`B${row}`).value = summaryData.totalIncome;
        summarySheet.getCell(`B${row}`).numFmt = '$#,##0.00';
        summarySheet.getCell(`B${row}`).font = { color: { argb: 'FF00AA00' } };
        
        row++;
        summarySheet.getCell(`A${row}`).value = 'Total Expenses:';
        summarySheet.getCell(`B${row}`).value = summaryData.totalExpenses;
        summarySheet.getCell(`B${row}`).numFmt = '$#,##0.00';
        summarySheet.getCell(`B${row}`).font = { color: { argb: 'FFFF0000' } };
        
        row++;
        summarySheet.getCell(`A${row}`).value = 'Net Amount:';
        summarySheet.getCell(`B${row}`).value = summaryData.netAmount;
        summarySheet.getCell(`B${row}`).numFmt = '$#,##0.00';
        summarySheet.getCell(`B${row}`).font = { 
            bold: true,
            color: { argb: summaryData.netAmount >= 0 ? 'FF00AA00' : 'FFFF0000' }
        };
        
        row += 2;
        summarySheet.getCell(`A${row}`).value = 'Transaction Count:';
        summarySheet.getCell(`B${row}`).value = summaryData.transactionCount;
        
        // Category breakdown
        row += 3;
        summarySheet.getCell(`A${row}`).value = 'Top Categories by Spending:';
        summarySheet.getCell(`A${row}`).font = { bold: true };
        
        summaryData.topCategories.forEach(category => {
            row++;
            summarySheet.getCell(`A${row}`).value = category.name;
            summarySheet.getCell(`B${row}`).value = parseFloat(category.total);
            summarySheet.getCell(`B${row}`).numFmt = '$#,##0.00';
        });
    }

    async getSummaryData(userId, filters) {
        const transactions = await this.getFilteredTransactions(userId, filters);
        
        const summary = {
            totalIncome: 0,
            totalExpenses: 0,
            netAmount: 0,
            transactionCount: transactions.length,
            topCategories: []
        };

        const categoryTotals = {};

        transactions.forEach(transaction => {
            const amount = parseFloat(transaction.amount);
            
            if (transaction.type === 'income') {
                summary.totalIncome += amount;
            } else if (transaction.type === 'expense') {
                summary.totalExpenses += amount;
                
                // Track category spending
                const categoryName = transaction.category_name || 'Uncategorized';
                categoryTotals[categoryName] = (categoryTotals[categoryName] || 0) + amount;
            }
        });

        summary.netAmount = summary.totalIncome - summary.totalExpenses;
        
        // Get top 5 categories
        summary.topCategories = Object.entries(categoryTotals)
            .map(([name, total]) => ({ name, total }))
            .sort((a, b) => b.total - a.total)
            .slice(0, 5);

        return summary;
    }

    async addChartsSheet(workbook, transactions) {
        const chartsSheet = workbook.addWorksheet('Charts Data');
        
        // Monthly spending data for charts
        const monthlyData = {};
        transactions.forEach(transaction => {
            if (transaction.type === 'expense') {
                const month = new Date(transaction.transaction_date).toISOString().slice(0, 7);
                monthlyData[month] = (monthlyData[month] || 0) + parseFloat(transaction.amount);
            }
        });

        chartsSheet.addRow(['Month', 'Amount']);
        Object.entries(monthlyData).forEach(([month, amount]) => {
            chartsSheet.addRow([month, amount]);
        });
    }
}

module.exports = new ExportService();