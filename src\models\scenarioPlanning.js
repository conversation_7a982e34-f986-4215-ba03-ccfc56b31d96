'use strict';

module.exports = (sequelize, DataTypes) => {
  const ScenarioPlanning = sequelize.define('ScenarioPlanning', {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true
    },
    companyId: {
      type: DataTypes.UUID,
      allowNull: false,
      references: {
        model: 'Companies',
        key: 'id'
      }
    },
    scenarioName: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    scenarioType: {
      type: DataTypes.ENUM('hiring', 'revenue_growth', 'cost_reduction', 'fundraising', 'custom'),
      allowNull: false
    },
    baseMonthlyBurn: {
      type: DataTypes.DECIMAL(15, 2),
      allowNull: false
    },
    projectedMonthlyBurn: {
      type: DataTypes.DECIMAL(15, 2),
      allowNull: false
    },
    impactOnRunway: {
      type: DataTypes.INTEGER,
      allowNull: false
    },
    parameters: {
      type: DataTypes.JSONB,
      defaultValue: {}
    },
    description: {
      type: DataTypes.TEXT
    },
    isActive: {
      type: DataTypes.BOOLEAN,
      defaultValue: true
    },
    createdBy: {
      type: DataTypes.UUID,
      references: {
        model: 'Users',
        key: 'id'
      }
    }
  }, {
    timestamps: true,
    indexes: [
      { fields: ['companyId'] },
      { fields: ['scenarioType'] },
      { fields: ['isActive'] }
    ]
  });

  ScenarioPlanning.associate = (models) => {
    ScenarioPlanning.belongsTo(models.Company, {
      foreignKey: 'companyId',
      as: 'company'
    });
    
    ScenarioPlanning.belongsTo(models.User, {
      foreignKey: 'createdBy',
      as: 'creator'
    });
  };

  return ScenarioPlanning;
};