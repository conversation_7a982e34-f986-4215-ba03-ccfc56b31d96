'use strict';

module.exports = (sequelize, DataTypes) => {
  const Transaction = sequelize.define('Transaction', {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true
    },
    bankAccountId: {
      type: DataTypes.UUID,
      allowNull: false,
      references: {
        model: 'BankAccounts',
        key: 'id'
      }
    },
    amount: {
      type: DataTypes.DECIMAL(19, 4),
      allowNull: false
    },
    currency: {
      type: DataTypes.STRING,
      defaultValue: 'USD'
    },
    date: {
      type: DataTypes.DATEONLY,
      allowNull: false
    },
    description: {
      type: DataTypes.TEXT
    },
    category: {
      type: DataTypes.STRING
    },
    merchantName: {
      type: DataTypes.STRING
    },
    merchantLogoUrl: {
      type: DataTypes.STRING
    },
    status: {
      type: DataTypes.ENUM('pending', 'cleared', 'voided'),
      defaultValue: 'pending'
    },
    transactionType: {
      type: DataTypes.ENUM('debit', 'credit'),
      allowNull: false
    },
    reference: {
      type: DataTypes.STRING
    },
    externalId: {
      type: DataTypes.STRING,
      unique: true
    },
    isReconciled: {
      type: DataTypes.BOOLEAN,
      defaultValue: false
    },
    isTransfer: {
      type: DataTypes.BOOLEAN,
      defaultValue: false
    },
    location: {
      type: DataTypes.STRING
    },
    tags: {
      type: DataTypes.ARRAY(DataTypes.STRING),
      defaultValue: []
    },
    notes: {
      type: DataTypes.TEXT
    }
  }, {
    timestamps: true,
    indexes: [
      { fields: ['bankAccountId'] },
      { fields: ['date'] },
      { fields: ['externalId'] },
      { fields: ['isReconciled'] }
    ]
  });

  Transaction.associate = (models) => {
    Transaction.belongsTo(models.BankAccount, {
      foreignKey: 'bankAccountId',
      as: 'bankAccount'
    });
    
    Transaction.hasMany(models.Expense, {
      foreignKey: 'transactionId',
      as: 'expenses'
    });
  };

  return Transaction;
};