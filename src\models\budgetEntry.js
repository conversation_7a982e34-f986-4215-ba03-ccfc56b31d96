'use strict';

module.exports = (sequelize, DataTypes) => {
  const BudgetEntry = sequelize.define('BudgetEntry', {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true
    },
    budgetId: {
      type: DataTypes.UUID,
      allowNull: false,
      references: {
        model: 'Budgets',
        key: 'id'
      }
    },
    amount: {
      type: DataTypes.DECIMAL(19, 4),
      allowNull: false
    },
    description: {
      type: DataTypes.TEXT
    },
    entryType: {
      type: DataTypes.ENUM('allocation', 'adjustment', 'transfer'),
      defaultValue: 'allocation'
    },
    date: {
      type: DataTypes.DATEONLY,
      allowNull: false
    },
    createdBy: {
      type: DataTypes.UUID,
      allowNull: false,
      references: {
        model: 'Users',
        key: 'id'
      }
    }
  }, {
    timestamps: true,
    indexes: [
      { fields: ['budgetId'] },
      { fields: ['createdBy'] },
      { fields: ['date'] }
    ]
  });

  BudgetEntry.associate = (models) => {
    BudgetEntry.belongsTo(models.Budget, {
      foreignKey: 'budgetId',
      as: 'budget'
    });
    
    BudgetEntry.belongsTo(models.User, {
      foreignKey: 'createdBy',
      as: 'creator'
    });
  };

  return BudgetEntry;
};