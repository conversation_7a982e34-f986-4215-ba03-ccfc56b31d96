'use strict';

const jwt = require('jsonwebtoken');
const bcrypt = require('bcryptjs');
const crypto = require('crypto');
const { User, Company, Department, UserSession, TwoFactorAuth } = require('../../../models');
const { ApiError } = require('../../../utils/apiError');
const auditService = require('../../../services/auditService');
const notificationService = require('../../../services/notificationService');
const logger = require('../../../utils/logger');

/**
 * Generate JWT tokens
 */
const generateTokens = (user) => {
  const accessToken = jwt.sign(
    {
      id: user.id,
      email: user.email,
      role: user.role
    },
    process.env.JWT_SECRET,
    { expiresIn: process.env.JWT_EXPIRATION || '1d' }
  );

  const refreshToken = jwt.sign(
    { id: user.id },
    process.env.JWT_SECRET,
    { expiresIn: process.env.JWT_REFRESH_EXPIRATION || '7d' }
  );

  return { accessToken, refreshToken };
};

/**
 * Create user session
 */
const createUserSession = async (user, req, tokens) => {
  const sessionToken = crypto.randomBytes(32).toString('hex');
  const expiresAt = new Date();
  expiresAt.setDate(expiresAt.getDate() + 7); // 7 days

  const session = await UserSession.create({
    userId: user.id,
    sessionToken,
    refreshToken: tokens.refreshToken,
    ipAddress: req.ip,
    userAgent: req.get('User-Agent'),
    deviceInfo: {
      platform: req.get('User-Agent'),
      ip: req.ip
    },
    expiresAt
  });

  return session;
};

/**
 * Register a new user
 */
const register = async (req, res, next) => {
  try {
    const { firstName, lastName, email, password, companyId, departmentId } = req.body;

    // Check if user already exists
    const existingUser = await User.findOne({ where: { email } });
    if (existingUser) {
      throw ApiError.conflict('User with this email already exists');
    }

    // Verify company and department if provided
    if (companyId) {
      const company = await Company.findByPk(companyId);
      if (!company) {
        throw ApiError.badRequest('Invalid company ID');
      }

      if (departmentId) {
        const department = await Department.findOne({
          where: { id: departmentId, companyId }
        });
        if (!department) {
          throw ApiError.badRequest('Invalid department ID for the provided company');
        }
      }
    }

    // Create user
    const user = await User.create({
      firstName,
      lastName,
      email,
      password,
      companyId,
      departmentId,
      role: 'employee' // Default role
    });

    // Generate tokens and create session
    const tokens = generateTokens(user);
    const session = await createUserSession(user, req, tokens);

    // Log audit event
    await auditService.logEvent({
      entityType: 'user',
      entityId: user.id,
      action: 'register',
      changes: { email, role: user.role },
      userId: user.id,
      ipAddress: req.ip,
      userAgent: req.get('User-Agent'),
      sessionId: session.sessionToken
    });

    // Create welcome notification
    await notificationService.createNotification({
      userId: user.id,
      title: 'Welcome to Finance Manager',
      message: 'Your account has been created successfully. Start by submitting your first expense!',
      type: 'system_update',
      priority: 'medium'
    });

    // Return user data without password
    const userData = user.toJSON();
    delete userData.password;

    res.status(201).json({
      status: 'success',
      data: {
        user: userData,
        accessToken: tokens.accessToken,
        refreshToken: tokens.refreshToken,
        sessionId: session.sessionToken
      }
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Login user
 */
const login = async (req, res, next) => {
  try {
    const { email, password, twoFactorCode } = req.body;

    // Find user
    const user = await User.findOne({ 
      where: { email },
      include: [{
        model: TwoFactorAuth,
        as: 'twoFactorAuth'
      }]
    });
    
    if (!user) {
      throw ApiError.unauthorized('Invalid credentials');
    }

    // Verify password
    const isPasswordValid = await user.comparePassword(password);
    if (!isPasswordValid) {
      throw ApiError.unauthorized('Invalid credentials');
    }

    // Check if user is active
    if (!user.isActive) {
      throw ApiError.unauthorized('Account is inactive. Please contact administrator.');
    }

    // Check 2FA if enabled
    if (user.twoFactorAuth && user.twoFactorAuth.isEnabled) {
      if (!twoFactorCode) {
        return res.status(200).json({
          status: 'success',
          requiresTwoFactor: true,
          message: 'Two-factor authentication required'
        });
      }

      const isValidCode = await verify2FACode(user.id, twoFactorCode);
      if (!isValidCode) {
        throw ApiError.unauthorized('Invalid two-factor authentication code');
      }
    }

    // Update last login timestamp
    user.lastLogin = new Date();
    await user.save();

    // Generate tokens and create session
    const tokens = generateTokens(user);
    const session = await createUserSession(user, req, tokens);

    // Log audit event
    await auditService.logEvent({
      entityType: 'user',
      entityId: user.id,
      action: 'login',
      changes: { lastLogin: user.lastLogin },
      userId: user.id,
      ipAddress: req.ip,
      userAgent: req.get('User-Agent'),
      sessionId: session.sessionToken
    });

    // Return user data without password
    const userData = user.toJSON();
    delete userData.password;

    res.status(200).json({
      status: 'success',
      data: {
        user: userData,
        accessToken: tokens.accessToken,
        refreshToken: tokens.refreshToken,
        sessionId: session.sessionToken
      }
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Logout user
 */
const logout = async (req, res, next) => {
  try {
    const sessionToken = req.headers['x-session-id'];
    
    if (sessionToken) {
      // Deactivate session
      await UserSession.update(
        { isActive: false },
        { where: { sessionToken, userId: req.user.id } }
      );
    }

    // Log audit event
    await auditService.logEvent({
      entityType: 'user',
      entityId: req.user.id,
      action: 'logout',
      changes: {},
      userId: req.user.id,
      ipAddress: req.ip,
      userAgent: req.get('User-Agent'),
      sessionId: sessionToken
    });
    
    res.status(200).json({
      status: 'success',
      message: 'Logged out successfully'
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Refresh token
 */
const refreshToken = async (req, res, next) => {
  try {
    const { refreshToken } = req.body;

    if (!refreshToken) {
      throw ApiError.badRequest('Refresh token is required');
    }

    // Verify refresh token
    const decoded = jwt.verify(refreshToken, process.env.JWT_SECRET);
    
    // Find user and session
    const [user, session] = await Promise.all([
      User.findByPk(decoded.id),
      UserSession.findOne({
        where: { 
          refreshToken, 
          isActive: true,
          expiresAt: { [require('sequelize').Op.gt]: new Date() }
        }
      })
    ]);

    if (!user || !user.isActive || !session) {
      throw ApiError.unauthorized('Invalid refresh token');
    }

    // Generate new tokens
    const tokens = generateTokens(user);
    
    // Update session with new refresh token
    session.refreshToken = tokens.refreshToken;
    session.lastActivity = new Date();
    await session.save();

    res.status(200).json({
      status: 'success',
      data: {
        accessToken: tokens.accessToken,
        refreshToken: tokens.refreshToken
      }
    });
  } catch (error) {
    if (error.name === 'JsonWebTokenError' || error.name === 'TokenExpiredError') {
      return next(ApiError.unauthorized('Invalid or expired refresh token'));
    }
    next(error);
  }
};

/**
 * Get current user
 */
const getCurrentUser = async (req, res, next) => {
  try {
    const user = await User.findByPk(req.user.id, {
      include: [
        {
          model: Company,
          as: 'company',
          attributes: ['id', 'name']
        },
        {
          model: Department,
          as: 'department',
          attributes: ['id', 'name']
        },
        {
          model: TwoFactorAuth,
          as: 'twoFactorAuth',
          attributes: ['isEnabled']
        }
      ],
      attributes: { exclude: ['password'] }
    });

    if (!user) {
      throw ApiError.notFound('User not found');
    }

    res.status(200).json({
      status: 'success',
      data: user
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Change password
 */
const changePassword = async (req, res, next) => {
  try {
    const { currentPassword, newPassword } = req.body;
    
    if (!currentPassword || !newPassword) {
      throw ApiError.badRequest('Current password and new password are required');
    }
    
    // Find user with password
    const user = await User.findByPk(req.user.id);
    
    // Verify current password
    const isPasswordValid = await user.comparePassword(currentPassword);
    if (!isPasswordValid) {
      throw ApiError.unauthorized('Current password is incorrect');
    }
    
    // Update password
    user.password = newPassword;
    await user.save();

    // Log audit event
    await auditService.logEvent({
      entityType: 'user',
      entityId: user.id,
      action: 'password_change',
      changes: { passwordChanged: true },
      userId: user.id,
      ipAddress: req.ip,
      userAgent: req.get('User-Agent')
    });

    // Create notification
    await notificationService.createNotification({
      userId: user.id,
      title: 'Password Changed',
      message: 'Your password has been successfully changed.',
      type: 'system_update',
      priority: 'medium'
    });
    
    res.status(200).json({
      status: 'success',
      message: 'Password changed successfully'
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Setup 2FA
 */
const setup2FA = async (req, res, next) => {
  try {
    const userId = req.user.id;
    
    // Generate secret and backup codes
    const secret = crypto.randomBytes(20).toString('hex');
    const backupCodes = Array.from({ length: 10 }, () => 
      crypto.randomBytes(4).toString('hex').toUpperCase()
    );

    // Create or update 2FA record
    const [twoFA] = await TwoFactorAuth.upsert({
      userId,
      secret,
      backupCodes,
      isEnabled: false // Will be enabled after verification
    });

    res.status(200).json({
      status: 'success',
      data: {
        secret,
        backupCodes,
        qrCodeUrl: `otpauth://totp/FinanceManager:${req.user.email}?secret=${secret}&issuer=FinanceManager`
      }
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Verify and enable 2FA
 */
const verify2FA = async (req, res, next) => {
  try {
    const { code } = req.body;
    const userId = req.user.id;

    if (!code) {
      throw ApiError.badRequest('Verification code is required');
    }

    const isValid = await verify2FACode(userId, code);
    if (!isValid) {
      throw ApiError.badRequest('Invalid verification code');
    }

    // Enable 2FA
    await TwoFactorAuth.update(
      { isEnabled: true },
      { where: { userId } }
    );

    // Log audit event
    await auditService.logEvent({
      entityType: 'user',
      entityId: userId,
      action: '2fa_enabled',
      changes: { twoFactorEnabled: true },
      userId,
      ipAddress: req.ip,
      userAgent: req.get('User-Agent')
    });

    res.status(200).json({
      status: 'success',
      message: 'Two-factor authentication enabled successfully'
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Disable 2FA
 */
const disable2FA = async (req, res, next) => {
  try {
    const { code } = req.body;
    const userId = req.user.id;

    if (!code) {
      throw ApiError.badRequest('Verification code is required');
    }

    const isValid = await verify2FACode(userId, code);
    if (!isValid) {
      throw ApiError.badRequest('Invalid verification code');
    }

    // Disable 2FA
    await TwoFactorAuth.update(
      { isEnabled: false },
      { where: { userId } }
    );

    // Log audit event
    await auditService.logEvent({
      entityType: 'user',
      entityId: userId,
      action: '2fa_disabled',
      changes: { twoFactorEnabled: false },
      userId,
      ipAddress: req.ip,
      userAgent: req.get('User-Agent')
    });

    res.status(200).json({
      status: 'success',
      message: 'Two-factor authentication disabled successfully'
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Get user sessions
 */
const getSessions = async (req, res, next) => {
  try {
    const sessions = await UserSession.findAll({
      where: { 
        userId: req.user.id,
        isActive: true,
        expiresAt: { [require('sequelize').Op.gt]: new Date() }
      },
      order: [['lastActivity', 'DESC']],
      attributes: ['id', 'sessionToken', 'ipAddress', 'deviceInfo', 'lastActivity', 'createdAt']
    });

    res.status(200).json({
      status: 'success',
      data: sessions
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Revoke session
 */
const revokeSession = async (req, res, next) => {
  try {
    const { sessionId } = req.params;
    
    const updated = await UserSession.update(
      { isActive: false },
      { 
        where: { 
          id: sessionId,
          userId: req.user.id 
        } 
      }
    );

    if (updated[0] === 0) {
      throw ApiError.notFound('Session not found');
    }

    res.status(200).json({
      status: 'success',
      message: 'Session revoked successfully'
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Helper function to verify 2FA code
 */
const verify2FACode = async (userId, code) => {
  try {
    const twoFA = await TwoFactorAuth.findOne({ where: { userId } });
    if (!twoFA) {
      return false;
    }

    // Check if it's a backup code
    if (twoFA.backupCodes.includes(code.toUpperCase())) {
      // Remove used backup code
      const updatedCodes = twoFA.backupCodes.filter(c => c !== code.toUpperCase());
      await twoFA.update({ backupCodes: updatedCodes });
      return true;
    }

    // For development, accept any 6-digit code
    // In production, you would use a proper TOTP library
    const isValidFormat = /^\d{6}$/.test(code);
    return isValidFormat;
  } catch (error) {
    logger.error('Error verifying 2FA code:', error);
    return false;
  }
};

module.exports = {
  register,
  login,
  logout,
  refreshToken,
  getCurrentUser,
  changePassword,
  setup2FA,
  verify2FA,
  disable2FA,
  getSessions,
  revokeSession
};