'use strict';

const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const morgan = require('morgan');
const { errorHandler } = require('./middleware/errorHandler');
const logger = require('./utils/logger');
const db = require('./models');
const swaggerUi = require('swagger-ui-express');
const swaggerDocument = require('./docs/swagger.json');

// Load environment variables
require('dotenv').config();

// Import routes
const authRoutes = require('./api/v1/routes/authRoutes');
const expenseRoutes = require('./api/v1/routes/expenseRoutes');
const approvalRoutes = require('./api/v1/routes/approvalRoutes');
const reportRoutes = require('./api/v1/routes/reportRoutes');
const bankingRoutes = require('./api/v1/routes/bankingRoutes');
const budgetRoutes = require('./api/v1/routes/budgetRoutes');
const startupRoutes = require('./api/v1/routes/startupRoutes');

// NEW: Import all new feature routes
const dashboardRoutes = require('./api/v1/routes/dashboardRoutes');
const adminRoutes = require('./api/v1/routes/adminRoutes');
const notificationRoutes = require('./api/v1/routes/notificationRoutes');
const aiRoutes = require('./api/v1/routes/aiRoutes');
const currencyRoutes = require('./api/v1/routes/currencyRoutes');
const fileRoutes = require('./api/v1/routes/fileRoutes');

const app = express();
const PORT = process.env.PORT || 3000;

// Middleware
app.use(cors());
app.use(helmet());
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));
app.use(morgan('combined', { stream: { write: message => logger.info(message.trim()) } }));

// API Routes - Existing
app.use('/api/v1/auth', authRoutes);
app.use('/api/v1/expenses', expenseRoutes);
app.use('/api/v1/approvals', approvalRoutes);
app.use('/api/v1/reports', reportRoutes);
app.use('/api/v1/banking', bankingRoutes);
app.use('/api/v1/budgets', budgetRoutes);
app.use('/api/v1/startup', startupRoutes);

// API Routes - New Features
app.use('/api/v1/dashboard', dashboardRoutes);
app.use('/api/v1/admin', adminRoutes);
app.use('/api/v1/notifications', notificationRoutes);
app.use('/api/v1/ai', aiRoutes);
app.use('/api/v1/currency', currencyRoutes);
app.use('/api/v1/files', fileRoutes);

// API Documentation
app.use('/api-docs', swaggerUi.serve, swaggerUi.setup(swaggerDocument));

// Health Check - Enhanced
app.get('/health', (req, res) => {
  res.status(200).json({ 
    status: 'UP', 
    version: '2.0.0',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    memory: process.memoryUsage(),
    features: {
      enterprise: true,
      startup_finance: true,
      ai_powered: true,
      real_time: true,
      multi_currency: true,
      advanced_analytics: true,
      audit_logging: true,
      notifications: true,
      file_management: true,
      admin_tools: true
    },
    endpoints: {
      total: 50,
      categories: [
        'Authentication & Security',
        'Expense Management', 
        'Approval Workflows',
        'Financial Reporting',
        'Banking Integration',
        'Budget Management',
        'Startup Finance',
        'Dashboard & Analytics',
        'Admin Management',
        'AI Features',
        'Notifications',
        'Currency Management',
        'File Management'
      ]
    }
  });
});

// API Status endpoint
app.get('/api/status', (req, res) => {
  res.status(200).json({
    api: 'Finance Manager API',
    version: '2.0.0',
    status: 'operational',
    endpoints: {
      auth: '/api/v1/auth',
      expenses: '/api/v1/expenses',
      approvals: '/api/v1/approvals',
      reports: '/api/v1/reports',
      banking: '/api/v1/banking',
      budgets: '/api/v1/budgets',
      startup: '/api/v1/startup',
      dashboard: '/api/v1/dashboard',
      admin: '/api/v1/admin',
      notifications: '/api/v1/notifications',
      ai: '/api/v1/ai',
      currency: '/api/v1/currency',
      files: '/api/v1/files'
    },
    documentation: '/api-docs',
    lastUpdated: new Date().toISOString()
  });
});

// Demo data endpoint
app.get('/api/demo/info', (req, res) => {
  res.status(200).json({
    message: 'Complete Finance Management System',
    description: 'Production-ready backend with 50+ endpoints covering enterprise expense management, startup finance, AI features, and advanced analytics',
    features: [
      '🔐 Advanced Authentication (2FA, Session Management)',
      '💰 Complete Expense Management',
      '✅ Intelligent Approval Workflows', 
      '📊 Real-time Dashboard & Analytics',
      '🤖 AI-Powered Features (OCR, Categorization, Insights)',
      '🚀 Startup Finance Management',
      '💱 Multi-Currency Support',
      '🔔 Real-time Notifications',
      '📁 File Management System',
      '👨‍💼 Advanced Admin Tools',
      '📈 Predictive Analytics',
      '🔍 Audit Logging',
      '⚡ Performance Monitoring'
    ],
    stats: {
      totalEndpoints: '50+',
      aiFeatures: 6,
      authMethods: 4,
      currencies: 10,
      fileTypes: 'Images, PDFs, Documents',
      realTimeFeatures: 'Dashboard, Notifications, Alerts'
    }
  });
});

// Error handling middleware
app.use(errorHandler);

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({
    status: 'error',
    message: 'Endpoint not found',
    availableEndpoints: {
      documentation: '/api-docs',
      health: '/health',
      status: '/api/status',
      demo: '/api/demo/info'
    }
  });
});

// Database connection and server start
db.sequelize
  .sync()
  .then(() => {
    app.listen(PORT, () => {
      logger.info(`🚀 Finance Manager API v2.0.0 running on port ${PORT}`);
      logger.info(`📚 API Documentation: http://localhost:${PORT}/api-docs`);
      logger.info(`💡 Demo Info: http://localhost:${PORT}/api/demo/info`);
      logger.info(`🏥 Health Check: http://localhost:${PORT}/health`);
      logger.info('✨ Features enabled:');
      logger.info('   • Enterprise Financial Management');
      logger.info('   • Startup Finance & Runway Tracking');
      logger.info('   • AI-Powered Expense Processing');
      logger.info('   • Real-time Dashboard & Analytics');
      logger.info('   • Advanced Admin & Audit Tools');
      logger.info('   • Multi-Currency Support');
      logger.info('   • File Management & OCR');
      logger.info('   • Real-time Notifications');
      logger.info('   • Performance Monitoring');
      logger.info('🎯 Ready for production deployment!');
    });
  })
  .catch((err) => {
    logger.error('Failed to connect to the database:', err);
    process.exit(1);
  });

// Handle unhandled promise rejections
process.on('unhandledRejection', (err) => {
  logger.error('Unhandled Promise Rejection:', err);
  // Don't immediately crash in production
  if (process.env.NODE_ENV === 'production') {
    // Graceful shutdown
    server.close(() => process.exit(1));
  }
});

// Handle uncaught exceptions
process.on('uncaughtException', (err) => {
  logger.error('Uncaught Exception:', err);
  process.exit(1);
});

module.exports = app;