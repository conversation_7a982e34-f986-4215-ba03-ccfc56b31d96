'use strict';

const path = require('path');
const fs = require('fs').promises;
const { FileUpload } = require('../../../models');
const { ApiError } = require('../../../utils/apiError');
const logger = require('../../../utils/logger');

/**
 * Upload file
 */
const uploadFile = async (req, res, next) => {
  try {
    if (!req.file) {
      throw ApiError.badRequest('No file uploaded');
    }

    const { entityType, entityId } = req.body;
    
    const fileUpload = await FileUpload.create({
      fileName: req.file.filename,
      originalName: req.file.originalname,
      fileType: req.file.mimetype,
      fileSize: req.file.size,
      filePath: req.file.path,
      uploadedBy: req.user.id,
      companyId: req.user.companyId,
      entityType,
      entityId,
      metadata: {
        encoding: req.file.encoding,
        destination: req.file.destination
      }
    });

    res.status(201).json({
      status: 'success',
      data: {
        id: fileUpload.id,
        fileName: fileUpload.fileName,
        originalName: fileUpload.originalName,
        fileType: fileUpload.fileType,
        fileSize: fileUpload.fileSize,
        uploadedAt: fileUpload.createdAt
      }
    });
  } catch (error) {
    logger.error('Error uploading file:', error);
    next(error);
  }
};

/**
 * Get file
 */
const getFile = async (req, res, next) => {
  try {
    const { fileId } = req.params;
    
    const file = await FileUpload.findOne({
      where: {
        id: fileId,
        companyId: req.user.companyId
      }
    });

    if (!file) {
      throw ApiError.notFound('File not found');
    }

    // Check if file exists on disk
    try {
      await fs.access(file.filePath);
    } catch (error) {
      throw ApiError.notFound('File not found on disk');
    }

    res.sendFile(path.resolve(file.filePath));
  } catch (error) {
    logger.error('Error getting file:', error);
    next(error);
  }
};

/**
 * Delete file
 */
const deleteFile = async (req, res, next) => {
  try {
    const { fileId } = req.params;
    
    const file = await FileUpload.findOne({
      where: {
        id: fileId,
        companyId: req.user.companyId
      }
    });

    if (!file) {
      throw ApiError.notFound('File not found');
    }

    // Delete file from disk
    try {
      await fs.unlink(file.filePath);
    } catch (error) {
      logger.warn('File not found on disk, continuing with database deletion');
    }

    // Delete thumbnail if exists
    if (file.thumbnailPath) {
      try {
        await fs.unlink(file.thumbnailPath);
      } catch (error) {
        logger.warn('Thumbnail not found on disk');
      }
    }

    // Delete from database
    await file.destroy();

    res.status(200).json({
      status: 'success',
      message: 'File deleted successfully'
    });
  } catch (error) {
    logger.error('Error deleting file:', error);
    next(error);
  }
};

/**
 * Get file thumbnail
 */
const getThumbnail = async (req, res, next) => {
  try {
    const { fileId } = req.params;
    
    const file = await FileUpload.findOne({
      where: {
        id: fileId,
        companyId: req.user.companyId
      }
    });

    if (!file) {
      throw ApiError.notFound('File not found');
    }

    // For now, return the original file if no thumbnail exists
    // In a real implementation, you would generate thumbnails for images
    const filePath = file.thumbnailPath || file.filePath;

    try {
      await fs.access(filePath);
    } catch (error) {
      throw ApiError.notFound('Thumbnail not found');
    }

    res.sendFile(path.resolve(filePath));
  } catch (error) {
    logger.error('Error getting thumbnail:', error);
    next(error);
  }
};

/**
 * Bulk upload files
 */
const bulkUpload = async (req, res, next) => {
  try {
    if (!req.files || req.files.length === 0) {
      throw ApiError.badRequest('No files uploaded');
    }

    const { entityType, entityId } = req.body;
    const uploadedFiles = [];

    for (const file of req.files) {
      try {
        const fileUpload = await FileUpload.create({
          fileName: file.filename,
          originalName: file.originalname,
          fileType: file.mimetype,
          fileSize: file.size,
          filePath: file.path,
          uploadedBy: req.user.id,
          companyId: req.user.companyId,
          entityType,
          entityId,
          metadata: {
            encoding: file.encoding,
            destination: file.destination
          }
        });

        uploadedFiles.push({
          id: fileUpload.id,
          fileName: fileUpload.fileName,
          originalName: fileUpload.originalName,
          fileType: fileUpload.fileType,
          fileSize: fileUpload.fileSize
        });
      } catch (error) {
        logger.error(`Error uploading file ${file.originalname}:`, error);
        // Continue with other files
      }
    }

    res.status(201).json({
      status: 'success',
      data: {
        uploadedFiles,
        totalUploaded: uploadedFiles.length,
        totalRequested: req.files.length
      }
    });
  } catch (error) {
    logger.error('Error in bulk upload:', error);
    next(error);
  }
};

/**
 * Get file metadata
 */
const getFileMetadata = async (req, res, next) => {
  try {
    const { fileId } = req.params;
    
    const file = await FileUpload.findOne({
      where: {
        id: fileId,
        companyId: req.user.companyId
      },
      include: [{
        model: require('../../../models').User,
        as: 'uploader',
        attributes: ['firstName', 'lastName', 'email']
      }]
    });

    if (!file) {
      throw ApiError.notFound('File not found');
    }

    res.status(200).json({
      status: 'success',
      data: {
        id: file.id,
        fileName: file.fileName,
        originalName: file.originalName,
        fileType: file.fileType,
        fileSize: file.fileSize,
        entityType: file.entityType,
        entityId: file.entityId,
        metadata: file.metadata,
        uploader: file.uploader,
        uploadedAt: file.createdAt,
        isProcessed: file.isProcessed
      }
    });
  } catch (error) {
    logger.error('Error getting file metadata:', error);
    next(error);
  }
};

module.exports = {
  uploadFile,
  getFile,
  deleteFile,
  getThumbnail,
  bulkUpload,
  getFileMetadata
};