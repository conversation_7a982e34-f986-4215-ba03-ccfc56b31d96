'use strict';

module.exports = (sequelize, DataTypes) => {
  const AutomationRule = sequelize.define('AutomationRule', {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true
    },
    companyId: {
      type: DataTypes.UUID,
      allowNull: false,
      references: {
        model: 'Companies',
        key: 'id'
      }
    },
    name: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    description: {
      type: DataTypes.TEXT
    },
    ruleType: {
      type: DataTypes.ENUM('categorization', 'approval', 'flagging', 'notification'),
      allowNull: false
    },
    conditions: {
      type: DataTypes.JSONB,
      allowNull: false
    },
    actions: {
      type: DataTypes.JSONB,
      allowNull: false
    },
    isActive: {
      type: DataTypes.BOOLEAN,
      defaultValue: true
    },
    priority: {
      type: DataTypes.INTEGER,
      defaultValue: 1
    },
    executionCount: {
      type: DataTypes.INTEGER,
      defaultValue: 0
    },
    lastExecuted: {
      type: DataTypes.DATE
    },
    createdBy: {
      type: DataTypes.UUID,
      allowNull: false,
      references: {
        model: 'Users',
        key: 'id'
      }
    }
  }, {
    timestamps: true,
    indexes: [
      { fields: ['companyId'] },
      { fields: ['ruleType'] },
      { fields: ['isActive'] },
      { fields: ['priority'] }
    ]
  });

  AutomationRule.associate = (models) => {
    AutomationRule.belongsTo(models.Company, {
      foreignKey: 'companyId',
      as: 'company'
    });
    
    AutomationRule.belongsTo(models.User, {
      foreignKey: 'createdBy',
      as: 'creator'
    });
  };

  return AutomationRule;
};