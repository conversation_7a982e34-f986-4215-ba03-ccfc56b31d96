'use strict';

module.exports = (sequelize, DataTypes) => {
  const Expense = sequelize.define('Expense', {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true
    },
    amount: {
      type: DataTypes.DECIMAL(19, 4),
      allowNull: false,
      validate: {
        isDecimal: true,
        min: 0
      }
    },
    currency: {
      type: DataTypes.STRING,
      defaultValue: 'USD'
    },
    exchangeRate: {
      type: DataTypes.DECIMAL(19, 6),
      defaultValue: 1.0
    },
    amountInBaseCurrency: {
      type: DataTypes.DECIMAL(19, 4),
      allowNull: false
    },
    description: {
      type: DataTypes.TEXT
    },
    expenseDate: {
      type: DataTypes.DATEONLY,
      allowNull: false
    },
    merchant: {
      type: DataTypes.STRING
    },
    location: {
      type: DataTypes.STRING
    },
    receiptUrl: {
      type: DataTypes.STRING
    },
    status: {
      type: DataTypes.ENUM(
        'draft', 
        'submitted', 
        'pending_approval', 
        'approved', 
        'rejected', 
        'reimbursed',
        'cancelled'
      ),
      defaultValue: 'draft'
    },
    paymentMethod: {
      type: DataTypes.ENUM(
        'company_card', 
        'personal_card', 
        'cash', 
        'bank_transfer', 
        'other'
      ),
      defaultValue: 'personal_card'
    },
    isPersonal: {
      type: DataTypes.BOOLEAN,
      defaultValue: false
    },
    isReimbursable: {
      type: DataTypes.BOOLEAN,
      defaultValue: true
    },
    notes: {
      type: DataTypes.TEXT
    },
    flags: {
      type: DataTypes.ARRAY(DataTypes.STRING),
      defaultValue: []
    },
    ocrConfidence: {
      type: DataTypes.DECIMAL(5, 2)
    },
    userId: {
      type: DataTypes.UUID,
      allowNull: false,
      references: {
        model: 'Users',
        key: 'id'
      }
    },
    categoryId: {
      type: DataTypes.UUID,
      references: {
        model: 'Categories',
        key: 'id'
      }
    },
    departmentId: {
      type: DataTypes.UUID,
      references: {
        model: 'Departments',
        key: 'id'
      }
    },
    projectId: {
      type: DataTypes.UUID,
      references: {
        model: 'Projects',
        key: 'id'
      }
    },
    companyId: {
      type: DataTypes.UUID,
      allowNull: false,
      references: {
        model: 'Companies',
        key: 'id'
      }
    },
    transactionId: {
      type: DataTypes.UUID,
      references: {
        model: 'Transactions',
        key: 'id'
      }
    }
  }, {
    timestamps: true,
    paranoid: true, // Soft deletes
    indexes: [
      { fields: ['userId'] },
      { fields: ['categoryId'] },
      { fields: ['status'] },
      { fields: ['expenseDate'] },
      { fields: ['departmentId'] },
      { fields: ['projectId'] },
      { fields: ['companyId'] }
    ]
  });

  Expense.associate = (models) => {
    Expense.belongsTo(models.User, {
      foreignKey: 'userId',
      as: 'user'
    });
    
    Expense.belongsTo(models.Category, {
      foreignKey: 'categoryId',
      as: 'category'
    });
    
    Expense.belongsTo(models.Department, {
      foreignKey: 'departmentId',
      as: 'department'
    });
    
    Expense.belongsTo(models.Company, {
      foreignKey: 'companyId',
      as: 'company'
    });
    
    // Only define associations for models that exist
    if (models.Project) {
      Expense.belongsTo(models.Project, {
        foreignKey: 'projectId',
        as: 'project'
      });
    }
    
    if (models.Transaction) {
      Expense.belongsTo(models.Transaction, {
        foreignKey: 'transactionId',
        as: 'transaction'
      });
    }
    
    if (models.Receipt) {
      Expense.hasMany(models.Receipt, {
        foreignKey: 'expenseId',
        as: 'receipts'
      });
    }
    
    if (models.Approval) {
      Expense.hasMany(models.Approval, {
        foreignKey: 'expenseId',
        as: 'approvals'
      });
    }
  };

  return Expense;
};