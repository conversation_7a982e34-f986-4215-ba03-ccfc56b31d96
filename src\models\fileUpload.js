'use strict';

module.exports = (sequelize, DataTypes) => {
  const FileUpload = sequelize.define('FileUpload', {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true
    },
    fileName: {
      type: DataTypes.STRING,
      allowNull: false
    },
    originalName: {
      type: DataTypes.STRING,
      allowNull: false
    },
    fileType: {
      type: DataTypes.STRING,
      allowNull: false
    },
    fileSize: {
      type: DataTypes.INTEGER,
      allowNull: false
    },
    filePath: {
      type: DataTypes.STRING,
      allowNull: false
    },
    uploadedBy: {
      type: DataTypes.UUID,
      allowNull: false,
      references: {
        model: 'Users',
        key: 'id'
      }
    },
    companyId: {
      type: DataTypes.UUID,
      allowNull: false,
      references: {
        model: 'Companies',
        key: 'id'
      }
    },
    entityType: {
      type: DataTypes.STRING(50)
    },
    entityId: {
      type: DataTypes.UUID
    },
    metadata: {
      type: DataTypes.JSONB,
      defaultValue: {}
    },
    isProcessed: {
      type: DataTypes.BOOLEAN,
      defaultValue: false
    },
    thumbnailPath: {
      type: DataTypes.STRING
    }
  }, {
    timestamps: true,
    indexes: [
      { fields: ['uploadedBy'] },
      { fields: ['companyId'] },
      { fields: ['entityType', 'entityId'] },
      { fields: ['fileType'] }
    ]
  });

  FileUpload.associate = (models) => {
    FileUpload.belongsTo(models.User, {
      foreignKey: 'uploadedBy',
      as: 'uploader'
    });
    
    FileUpload.belongsTo(models.Company, {
      foreignKey: 'companyId',
      as: 'company'
    });
  };

  return FileUpload;
};