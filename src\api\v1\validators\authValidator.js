'use strict';

const { body } = require('express-validator');
const { validationMiddleware } = require('../../../middleware/validation');

const validateRegister = [
  body('firstName')
    .notEmpty().withMessage('First name is required')
    .isLength({ min: 2, max: 50 }).withMessage('First name must be between 2 and 50 characters'),
  
  body('lastName')
    .notEmpty().withMessage('Last name is required')
    .isLength({ min: 2, max: 50 }).withMessage('Last name must be between 2 and 50 characters'),
  
  body('email')
    .notEmpty().withMessage('Email is required')
    .isEmail().withMessage('Must be a valid email address')
    .normalizeEmail(),
  
  body('password')
    .notEmpty().withMessage('Password is required')
    .isLength({ min: 6 }).withMessage('Password must be at least 6 characters long')
    .matches(/\d/).withMessage('Password must contain at least one number'),
  
  body('companyId')
    .optional()
    .isUUID().withMessage('Company ID must be a valid UUID'),
  
  body('departmentId')
    .optional()
    .isUUID().withMessage('Department ID must be a valid UUID'),
  
  validationMiddleware
];

const validateLogin = [
  body('email')
    .notEmpty().withMessage('Email is required')
    .isEmail().withMessage('Must be a valid email address')
    .normalizeEmail(),
  
  body('password')
    .notEmpty().withMessage('Password is required'),
  
  validationMiddleware
];

module.exports = {
  validateRegister,
  validateLogin
};