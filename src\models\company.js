'use strict';

module.exports = (sequelize, DataTypes) => {
  const Company = sequelize.define('Company', {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true
    },
    name: {
      type: DataTypes.STRING,
      allowNull: false,
      unique: true
    },
    legalName: {
      type: DataTypes.STRING
    },
    taxId: {
      type: DataTypes.STRING
    },
    address: {
      type: DataTypes.TEXT
    },
    city: {
      type: DataTypes.STRING
    },
    state: {
      type: DataTypes.STRING
    },
    zipCode: {
      type: DataTypes.STRING
    },
    country: {
      type: DataTypes.STRING
    },
    phone: {
      type: DataTypes.STRING
    },
    email: {
      type: DataTypes.STRING,
      validate: {
        isEmail: true
      }
    },
    website: {
      type: DataTypes.STRING
    },
    logoUrl: {
      type: DataTypes.STRING
    },
    baseCurrency: {
      type: DataTypes.STRING,
      defaultValue: 'USD'
    },
    fiscalYearStart: {
      type: DataTypes.DATEONLY
    },
    status: {
      type: DataTypes.ENUM('active', 'inactive', 'suspended'),
      defaultValue: 'active'
    }
  }, {
    timestamps: true,
    paranoid: true // Soft deletes
  });

  Company.associate = (models) => {
    Company.hasMany(models.User, {
      foreignKey: 'companyId',
      as: 'users'
    });
    
    Company.hasMany(models.Department, {
      foreignKey: 'companyId',
      as: 'departments'
    });
    
    Company.hasMany(models.BankAccount, {
      foreignKey: 'companyId',
      as: 'bankAccounts'
    });
    
    Company.hasMany(models.Budget, {
      foreignKey: 'companyId',
      as: 'budgets'
    });

    Company.hasMany(models.Expense, {
      foreignKey: 'companyId',
      as: 'expenses'
    });

    Company.hasMany(models.Category, {
      foreignKey: 'companyId',
      as: 'categories'
    });

    Company.hasMany(models.CashPosition, {
      foreignKey: 'companyId',
      as: 'cashPositions'
    });

    Company.hasMany(models.BurnRateCalculation, {
      foreignKey: 'companyId',
      as: 'burnRateCalculations'
    });

    Company.hasMany(models.RunwayAlert, {
      foreignKey: 'companyId',
      as: 'runwayAlerts'
    });

    Company.hasMany(models.ScenarioPlanning, {
      foreignKey: 'companyId',
      as: 'scenarios'
    });
  };

  return Company;
};