'use strict';

const { Op } = require('sequelize');
const { 
  Expense, 
  Budget, 
  Approval, 
  User, 
  Category, 
  Company,
  CashPosition,
  BurnRateCalculation,
  RunwayAlert,
  Notification
} = require('../../../models');
const { ApiError } = require('../../../utils/apiError');
const startupFinanceService = require('../../../services/startupFinanceService');
const logger = require('../../../utils/logger');

/**
 * Get dashboard summary with all key metrics
 */
const getDashboardSummary = async (req, res, next) => {
  try {
    const companyId = req.user.companyId;
    const userId = req.user.id;
    
    if (!companyId) {
      throw ApiError.badRequest('User must be associated with a company');
    }

    // Date ranges
    const now = new Date();
    const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);
    const startOfLastMonth = new Date(now.getFullYear(), now.getMonth() - 1, 1);
    const endOfLastMonth = new Date(now.getFullYear(), now.getMonth(), 0);
    const startOfYear = new Date(now.getFullYear(), 0, 1);

    // Parallel data fetching for performance
    const [
      currentMonthExpenses,
      lastMonthExpenses,
      yearToDateExpenses,
      pendingApprovals,
      budgetUtilization,
      recentTransactions,
      notifications,
      startupMetrics
    ] = await Promise.all([
      // Current month expenses
      Expense.findAll({
        where: {
          companyId,
          expenseDate: { [Op.gte]: startOfMonth },
          status: 'approved'
        },
        attributes: ['amountInBaseCurrency', 'categoryId'],
        include: [{
          model: Category,
          as: 'category',
          attributes: ['name']
        }]
      }),

      // Last month expenses
      Expense.sum('amountInBaseCurrency', {
        where: {
          companyId,
          expenseDate: { [Op.between]: [startOfLastMonth, endOfLastMonth] },
          status: 'approved'
        }
      }),

      // Year to date expenses
      Expense.sum('amountInBaseCurrency', {
        where: {
          companyId,
          expenseDate: { [Op.gte]: startOfYear },
          status: 'approved'
        }
      }),

      // Pending approvals
      Approval.findAll({
        where: { status: 'pending' },
        include: [{
          model: Expense,
          as: 'expense',
          where: { companyId },
          include: [{
            model: User,
            as: 'user',
            attributes: ['firstName', 'lastName']
          }]
        }],
        order: [['createdAt', 'ASC']]
      }),

      // Budget utilization
      getBudgetUtilization(companyId, startOfMonth),

      // Recent transactions
      Expense.findAll({
        where: { companyId },
        order: [['createdAt', 'DESC']],
        limit: 10,
        include: [
          {
            model: User,
            as: 'user',
            attributes: ['firstName', 'lastName']
          },
          {
            model: Category,
            as: 'category',
            attributes: ['name']
          }
        ]
      }),

      // Notifications
      Notification.count({
        where: { userId, isRead: false }
      }),

      // Startup metrics (if applicable)
      getStartupMetrics(companyId)
    ]);

    // Process current month data
    const currentMonthTotal = currentMonthExpenses.reduce(
      (sum, expense) => sum + parseFloat(expense.amountInBaseCurrency), 0
    );

    const expensesByCategory = currentMonthExpenses.reduce((acc, expense) => {
      const categoryName = expense.category?.name || 'Uncategorized';
      acc[categoryName] = (acc[categoryName] || 0) + parseFloat(expense.amountInBaseCurrency);
      return acc;
    }, {});

    // Calculate trends
    const monthOverMonthChange = lastMonthExpenses ? 
      ((currentMonthTotal - lastMonthExpenses) / lastMonthExpenses * 100) : 0;

    // Approval metrics
    const urgentApprovals = pendingApprovals.filter(approval => 
      parseFloat(approval.expense.amountInBaseCurrency) > 1000
    ).length;

    const approvalVelocity = await calculateApprovalVelocity(companyId);

    // Build response
    const dashboardData = {
      spending: {
        currentMonth: currentMonthTotal,
        lastMonth: lastMonthExpenses || 0,
        yearToDate: yearToDateExpenses || 0,
        monthOverMonthChange,
        trend: monthOverMonthChange > 0 ? 'increasing' : 'decreasing',
        byCategory: expensesByCategory
      },
      approvals: {
        pending: pendingApprovals.length,
        urgent: urgentApprovals,
        averageProcessingTime: approvalVelocity.averageTime,
        oldestPending: approvalVelocity.oldestPending
      },
      budgets: budgetUtilization,
      recentActivity: recentTransactions.map(expense => ({
        id: expense.id,
        amount: expense.amountInBaseCurrency,
        description: expense.description,
        category: expense.category?.name,
        user: `${expense.user.firstName} ${expense.user.lastName}`,
        date: expense.expenseDate,
        status: expense.status
      })),
      notifications: {
        unread: notifications
      },
      alerts: await getActiveAlerts(companyId),
      lastUpdated: new Date()
    };

    // Add startup metrics if available
    if (startupMetrics) {
      dashboardData.startup = startupMetrics;
    }

    res.status(200).json({
      status: 'success',
      data: dashboardData
    });
  } catch (error) {
    logger.error('Error getting dashboard summary:', error);
    next(error);
  }
};

/**
 * Get real-time statistics
 */
const getRealTimeStats = async (req, res, next) => {
  try {
    const companyId = req.user.companyId;
    
    if (!companyId) {
      throw ApiError.badRequest('User must be associated with a company');
    }

    const now = new Date();
    const last24Hours = new Date(now.getTime() - 24 * 60 * 60 * 1000);
    const lastHour = new Date(now.getTime() - 60 * 60 * 1000);

    const [
      expensesLast24h,
      expensesLastHour,
      approvalsLast24h,
      activeUsers,
      systemHealth
    ] = await Promise.all([
      Expense.count({
        where: {
          companyId,
          createdAt: { [Op.gte]: last24Hours }
        }
      }),
      Expense.count({
        where: {
          companyId,
          createdAt: { [Op.gte]: lastHour }
        }
      }),
      Approval.count({
        where: {
          createdAt: { [Op.gte]: last24Hours }
        },
        include: [{
          model: Expense,
          as: 'expense',
          where: { companyId }
        }]
      }),
      User.count({
        where: {
          companyId,
          lastLogin: { [Op.gte]: last24Hours }
        }
      }),
      getSystemHealth()
    ]);

    res.status(200).json({
      status: 'success',
      data: {
        expenses: {
          last24Hours: expensesLast24h,
          lastHour: expensesLastHour
        },
        approvals: {
          last24Hours: approvalsLast24h
        },
        users: {
          activeToday: activeUsers
        },
        system: systemHealth,
        timestamp: new Date()
      }
    });
  } catch (error) {
    logger.error('Error getting real-time stats:', error);
    next(error);
  }
};

/**
 * Get performance metrics
 */
const getPerformanceMetrics = async (req, res, next) => {
  try {
    const companyId = req.user.companyId;
    
    if (!companyId) {
      throw ApiError.badRequest('User must be associated with a company');
    }

    const metrics = {
      database: await getDatabaseMetrics(),
      api: await getApiMetrics(),
      processing: await getProcessingMetrics(companyId),
      timestamp: new Date()
    };

    res.status(200).json({
      status: 'success',
      data: metrics
    });
  } catch (error) {
    logger.error('Error getting performance metrics:', error);
    next(error);
  }
};

// Helper functions

/**
 * Get budget utilization data
 */
const getBudgetUtilization = async (companyId, startOfMonth) => {
  try {
    const budgets = await Budget.findAll({
      where: {
        companyId,
        startDate: { [Op.lte]: startOfMonth },
        endDate: { [Op.gte]: startOfMonth },
        isActive: true
      },
      include: [{
        model: Category,
        as: 'category',
        attributes: ['name']
      }]
    });

    const utilizationData = [];

    for (const budget of budgets) {
      const spent = await Expense.sum('amountInBaseCurrency', {
        where: {
          companyId,
          categoryId: budget.categoryId,
          expenseDate: { [Op.gte]: startOfMonth },
          status: 'approved'
        }
      }) || 0;

      const utilization = (spent / parseFloat(budget.amount)) * 100;
      
      utilizationData.push({
        category: budget.category?.name || 'Unknown',
        budgeted: parseFloat(budget.amount),
        spent,
        remaining: parseFloat(budget.amount) - spent,
        utilization: Math.round(utilization * 100) / 100,
        status: utilization > 100 ? 'over' : utilization > 80 ? 'warning' : 'good'
      });
    }

    return utilizationData;
  } catch (error) {
    logger.error('Error getting budget utilization:', error);
    return [];
  }
};

/**
 * Calculate approval velocity metrics
 */
const calculateApprovalVelocity = async (companyId) => {
  try {
    const recentApprovals = await Approval.findAll({
      where: {
        status: { [Op.in]: ['approved', 'rejected'] },
        actionDate: { [Op.gte]: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) }
      },
      include: [{
        model: Expense,
        as: 'expense',
        where: { companyId }
      }]
    });

    const processingTimes = recentApprovals.map(approval => {
      const created = new Date(approval.createdAt);
      const processed = new Date(approval.actionDate);
      return (processed - created) / (1000 * 60 * 60); // Hours
    });

    const averageTime = processingTimes.length > 0 ? 
      processingTimes.reduce((sum, time) => sum + time, 0) / processingTimes.length : 0;

    // Get oldest pending approval
    const oldestPending = await Approval.findOne({
      where: { status: 'pending' },
      include: [{
        model: Expense,
        as: 'expense',
        where: { companyId }
      }],
      order: [['createdAt', 'ASC']]
    });

    const oldestPendingHours = oldestPending ? 
      (new Date() - new Date(oldestPending.createdAt)) / (1000 * 60 * 60) : 0;

    return {
      averageTime: Math.round(averageTime * 100) / 100,
      oldestPending: Math.round(oldestPendingHours * 100) / 100
    };
  } catch (error) {
    logger.error('Error calculating approval velocity:', error);
    return { averageTime: 0, oldestPending: 0 };
  }
};

/**
 * Get startup metrics if applicable
 */
const getStartupMetrics = async (companyId) => {
  try {
    const runway = await startupFinanceService.calculateRunway(companyId);
    const healthScore = await startupFinanceService.calculateHealthScore(companyId);
    
    const alerts = await RunwayAlert.count({
      where: { companyId, isActive: true }
    });

    return {
      cashPosition: runway.currentCash,
      monthlyBurn: runway.avgMonthlyBurn,
      runwayMonths: runway.runwayMonths,
      runwayDays: runway.runwayDays,
      healthScore: healthScore.healthScore,
      activeAlerts: alerts
    };
  } catch (error) {
    logger.error('Error getting startup metrics:', error);
    return null;
  }
};

/**
 * Get active alerts
 */
const getActiveAlerts = async (companyId) => {
  try {
    const [budgetAlerts, runwayAlerts] = await Promise.all([
      // Budget alerts (simulated)
      Budget.findAll({
        where: {
          companyId,
          isActive: true
        },
        include: [{
          model: Category,
          as: 'category',
          attributes: ['name']
        }]
      }),
      
      // Runway alerts
      RunwayAlert.findAll({
        where: {
          companyId,
          isActive: true
        },
        order: [['severity', 'DESC'], ['createdAt', 'DESC']],
        limit: 5
      })
    ]);

    const alerts = [];

    // Add runway alerts
    runwayAlerts.forEach(alert => {
      alerts.push({
        type: alert.alertType,
        severity: alert.severity,
        message: alert.message,
        createdAt: alert.createdAt
      });
    });

    return alerts;
  } catch (error) {
    logger.error('Error getting active alerts:', error);
    return [];
  }
};

/**
 * Get system health metrics
 */
const getSystemHealth = async () => {
  try {
    return {
      status: 'healthy',
      uptime: process.uptime(),
      memory: process.memoryUsage(),
      cpu: process.cpuUsage()
    };
  } catch (error) {
    return { status: 'unknown' };
  }
};

/**
 * Get database metrics
 */
const getDatabaseMetrics = async () => {
  try {
    const { sequelize } = require('../../../models');
    
    // Simple query performance test
    const start = Date.now();
    await sequelize.query('SELECT 1');
    const queryTime = Date.now() - start;

    return {
      connectionStatus: 'connected',
      queryTime,
      poolSize: sequelize.connectionManager.pool.size
    };
  } catch (error) {
    return {
      connectionStatus: 'error',
      queryTime: null,
      poolSize: 0
    };
  }
};

/**
 * Get API metrics
 */
const getApiMetrics = async () => {
  return {
    responseTime: Math.random() * 100 + 50, // Simulated
    requestsPerMinute: Math.floor(Math.random() * 100) + 20,
    errorRate: Math.random() * 5
  };
};

/**
 * Get processing metrics
 */
const getProcessingMetrics = async (companyId) => {
  try {
    const last24Hours = new Date(Date.now() - 24 * 60 * 60 * 1000);
    
    const [processedExpenses, processedApprovals] = await Promise.all([
      Expense.count({
        where: {
          companyId,
          updatedAt: { [Op.gte]: last24Hours }
        }
      }),
      Approval.count({
        where: {
          actionDate: { [Op.gte]: last24Hours }
        },
        include: [{
          model: Expense,
          as: 'expense',
          where: { companyId }
        }]
      })
    ]);

    return {
      expensesProcessed24h: processedExpenses,
      approvalsProcessed24h: processedApprovals,
      averageProcessingTime: Math.random() * 10 + 5 // Simulated
    };
  } catch (error) {
    return {
      expensesProcessed24h: 0,
      approvalsProcessed24h: 0,
      averageProcessingTime: 0
    };
  }
};

module.exports = {
  getDashboardSummary,
  getRealTimeStats,
  getPerformanceMetrics
};