'use strict';

const axios = require('axios');
const logger = require('../utils/logger');
const { BankAccount, Transaction } = require('../models');
const { ApiError } = require('../utils/apiError');

/**
 * Service for banking integration using Plaid API
 */
class BankingService {
  constructor() {
    this.plaidClientId = process.env.PLAID_CLIENT_ID;
    this.plaidSecret = process.env.PLAID_SECRET;
    this.plaidUrl = 'https://sandbox.plaid.com'; // Use sandbox for development
  }

  /**
   * Create a link token for Plaid Link initialization
   * @param {string} userId - User ID
   * @returns {Promise<Object>} Link token
   */
  async createLinkToken(userId) {
    try {
      logger.info(`Creating Plaid link token for user: ${userId}`);
      
      // In production, this would make a real API call to Plaid
      // This is a simplified implementation
      return {
        link_token: `link-sandbox-${Math.random().toString(36).substring(2, 15)}`,
        expiration: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString()
      };
    } catch (error) {
      logger.error('Error creating Plaid link token:', error);
      throw new ApiError('Failed to create link token', 500);
    }
  }

  /**
   * Exchange public token for access token
   * @param {string} publicToken - Public token from Plaid Link
   * @returns {Promise<Object>} Access token and item ID
   */
  async exchangePublicToken(publicToken) {
    try {
      logger.info('Exchanging Plaid public token');
      
      // In production, this would make a real API call to Plaid
      // This is a simplified implementation
      return {
        access_token: `access-sandbox-${Math.random().toString(36).substring(2, 15)}`,
        item_id: `item-sandbox-${Math.random().toString(36).substring(2, 15)}`
      };
    } catch (error) {
      logger.error('Error exchanging Plaid public token:', error);
      throw new ApiError('Failed to exchange public token', 500);
    }
  }

  /**
   * Get account information for a bank connection
   * @param {string} accessToken - Plaid access token
   * @returns {Promise<Array>} List of accounts
   */
  async getAccounts(accessToken) {
    try {
      logger.info('Getting bank accounts');
      
      // In production, this would make a real API call to Plaid
      // This is a simplified implementation
      return [
        {
          account_id: `account-${Math.random().toString(36).substring(2, 10)}`,
          name: 'Checking Account',
          type: 'depository',
          subtype: 'checking',
          mask: '1234',
          balances: {
            available: 12345.67,
            current: 12345.67,
            limit: null
          }
        },
        {
          account_id: `account-${Math.random().toString(36).substring(2, 10)}`,
          name: 'Corporate Credit Card',
          type: 'credit',
          subtype: 'credit card',
          mask: '5678',
          balances: {
            available: 10000,
            current: 2456.78,
            limit: 15000
          }
        }
      ];
    } catch (error) {
      logger.error('Error getting accounts:', error);
      throw new ApiError('Failed to get accounts', 500);
    }
  }

  /**
   * Sync transactions for a bank account
   * @param {string} bankAccountId - Bank account ID in our system
   * @returns {Promise<Object>} Transaction sync results
   */
  async syncTransactions(bankAccountId) {
    try {
      logger.info(`Syncing transactions for bank account: ${bankAccountId}`);
      
      // Get bank account from database
      const bankAccount = await BankAccount.findByPk(bankAccountId);
      
      if (!bankAccount) {
        throw new ApiError('Bank account not found', 404);
      }
      
      if (!bankAccount.plaidAccessToken) {
        throw new ApiError('Bank account not connected to Plaid', 400);
      }
      
      // In production, this would make a real API call to Plaid
      // This is a simplified implementation
      const transactions = this._simulateTransactions(bankAccount);
      
      // Process and save transactions to database
      const results = await this._processTransactions(bankAccount, transactions);
      
      // Update last sync date
      bankAccount.lastSyncDate = new Date();
      await bankAccount.save();
      
      return results;
    } catch (error) {
      logger.error(`Error syncing transactions: ${error.message}`, error);
      throw error instanceof ApiError ? error : new ApiError('Failed to sync transactions', 500);
    }
  }

  /**
   * Process and save transactions to database
   * @param {Object} bankAccount - Bank account model instance
   * @param {Array} transactions - Transactions from Plaid
   * @returns {Promise<Object>} Processing results
   */
  async _processTransactions(bankAccount, transactions) {
    let added = 0;
    let updated = 0;
    let skipped = 0;
    
    for (const transaction of transactions) {
      try {
        // Check if transaction already exists
        const existingTransaction = await Transaction.findOne({
          where: { externalId: transaction.transaction_id }
        });
        
        if (existingTransaction) {
          // Update existing transaction
          await existingTransaction.update({
            amount: transaction.amount,
            currency: transaction.iso_currency_code || 'USD',
            date: new Date(transaction.date),
            description: transaction.name,
            merchantName: transaction.merchant_name,
            category: transaction.category ? transaction.category.join(', ') : null,
            status: transaction.pending ? 'pending' : 'cleared',
            location: transaction.location ? JSON.stringify(transaction.location) : null
          });
          
          updated++;
        } else {
          // Create new transaction
          await Transaction.create({
            bankAccountId: bankAccount.id,
            externalId: transaction.transaction_id,
            amount: Math.abs(transaction.amount),
            currency: transaction.iso_currency_code || 'USD',
            date: new Date(transaction.date),
            description: transaction.name,
            merchantName: transaction.merchant_name,
            category: transaction.category ? transaction.category.join(', ') : null,
            status: transaction.pending ? 'pending' : 'cleared',
            transactionType: transaction.amount < 0 ? 'debit' : 'credit',
            reference: transaction.payment_meta ? transaction.payment_meta.reference_number : null,
            location: transaction.location ? JSON.stringify(transaction.location) : null,
            isReconciled: false
          });
          
          added++;
        }
      } catch (error) {
        logger.error(`Error processing transaction: ${error.message}`);
        skipped++;
      }
    }
    
    return { added, updated, skipped, total: transactions.length };
  }

  /**
   * Simulate getting transactions from Plaid
   * In production, this would make a real API call to Plaid
   */
  _simulateTransactions(bankAccount) {
    // Generate random number of transactions (5-20)
    const count = Math.floor(Math.random() * 15) + 5;
    const transactions = [];
    
    // Sample merchants and categories for realistic data
    const merchants = [
      { name: 'Amazon', categories: ['Merchandise', 'Shopping'] },
      { name: 'Starbucks', categories: ['Food and Drink', 'Coffee Shop'] },
      { name: 'Uber', categories: ['Travel', 'Transportation'] },
      { name: 'Office Depot', categories: ['Office Supplies', 'Business'] },
      { name: 'Delta Airlines', categories: ['Travel', 'Airlines'] },
      { name: 'Hilton Hotels', categories: ['Travel', 'Lodging'] },
      { name: 'Shell', categories: ['Transportation', 'Gas'] }
    ];
    
    // Generate random transactions
    for (let i = 0; i < count; i++) {
      const merchant = merchants[Math.floor(Math.random() * merchants.length)];
      const isDebit = Math.random() > 0.3; // 70% chance for debit transactions
      const amount = isDebit ? 
        -1 * (Math.random() * 500 + 10).toFixed(2) : 
        (Math.random() * 2000 + 100).toFixed(2);
      
      // Calculate date within last 30 days
      const date = new Date();
      date.setDate(date.getDate() - Math.floor(Math.random() * 30));
      
      transactions.push({
        transaction_id: `tx-${Math.random().toString(36).substring(2, 15)}`,
        account_id: bankAccount.plaidAccountId,
        amount: parseFloat(amount),
        iso_currency_code: bankAccount.currency,
        date: date.toISOString().split('T')[0],
        name: `${merchant.name} Transaction`,
        merchant_name: merchant.name,
        category: merchant.categories,
        pending: Math.random() > 0.9, // 10% chance for pending transactions
        payment_meta: {
          reference_number: `REF-${Math.floor(Math.random() * 10000)}`
        },
        location: {
          address: '123 Business St',
          city: 'New York',
          region: 'NY',
          postal_code: '10001',
          country: 'US'
        }
      });
    }
    
    return transactions;
  }
}

module.exports = new BankingService();