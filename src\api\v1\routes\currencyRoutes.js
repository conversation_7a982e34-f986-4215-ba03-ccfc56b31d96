'use strict';

const express = require('express');
const { authenticate } = require('../../../middleware/auth');
const currencyController = require('../controllers/currencyController');

const router = express.Router();

// All routes require authentication
router.use(authenticate);

/**
 * @swagger
 * /api/v1/currency/rates:
 *   get:
 *     summary: Get current exchange rates
 *     tags: [Currency]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: base
 *         schema:
 *           type: string
 *           default: USD
 *         description: Base currency code
 *     responses:
 *       200:
 *         description: Exchange rates retrieved successfully
 */
router.get('/rates', currencyController.getCurrentRates);

/**
 * @swagger
 * /api/v1/currency/supported:
 *   get:
 *     summary: Get supported currencies
 *     tags: [Currency]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Supported currencies retrieved successfully
 */
router.get('/supported', currencyController.getSupportedCurrencies);

/**
 * @swagger
 * /api/v1/currency/convert:
 *   post:
 *     summary: Convert between currencies
 *     tags: [Currency]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - amount
 *               - from
 *               - to
 *             properties:
 *               amount:
 *                 type: number
 *               from:
 *                 type: string
 *                 description: Source currency code
 *               to:
 *                 type: string
 *                 description: Target currency code
 *               date:
 *                 type: string
 *                 format: date
 *                 description: Date for historical conversion
 *     responses:
 *       200:
 *         description: Currency converted successfully
 */
router.post('/convert', currencyController.convertCurrency);

/**
 * @swagger
 * /api/v1/currency/history:
 *   get:
 *     summary: Get historical exchange rates
 *     tags: [Currency]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: from
 *         required: true
 *         schema:
 *           type: string
 *         description: Source currency code
 *       - in: query
 *         name: to
 *         required: true
 *         schema:
 *           type: string
 *         description: Target currency code
 *       - in: query
 *         name: startDate
 *         required: true
 *         schema:
 *           type: string
 *           format: date
 *       - in: query
 *         name: endDate
 *         required: true
 *         schema:
 *           type: string
 *           format: date
 *     responses:
 *       200:
 *         description: Historical rates retrieved successfully
 */
router.get('/history', currencyController.getHistoricalRates);

module.exports = router;