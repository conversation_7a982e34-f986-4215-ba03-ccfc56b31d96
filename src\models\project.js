'use strict';

module.exports = (sequelize, DataTypes) => {
  const Project = sequelize.define('Project', {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true
    },
    name: {
      type: DataTypes.STRING,
      allowNull: false
    },
    description: {
      type: DataTypes.TEXT
    },
    code: {
      type: DataTypes.STRING
    },
    companyId: {
      type: DataTypes.UUID,
      allowNull: false,
      references: {
        model: 'Companies',
        key: 'id'
      }
    },
    managerId: {
      type: DataTypes.UUID,
      references: {
        model: 'Users',
        key: 'id'
      }
    },
    budget: {
      type: DataTypes.DECIMAL(19, 4)
    },
    startDate: {
      type: DataTypes.DATEONLY
    },
    endDate: {
      type: DataTypes.DATEONLY
    },
    status: {
      type: DataTypes.ENUM('active', 'completed', 'on_hold', 'cancelled'),
      defaultValue: 'active'
    },
    isActive: {
      type: DataTypes.BOOLEAN,
      defaultValue: true
    }
  }, {
    timestamps: true,
    paranoid: true,
    indexes: [
      { fields: ['companyId'] },
      { fields: ['managerId'] },
      { fields: ['status'] }
    ]
  });

  Project.associate = (models) => {
    Project.belongsTo(models.Company, {
      foreignKey: 'companyId',
      as: 'company'
    });
    
    Project.belongsTo(models.User, {
      foreignKey: 'managerId',
      as: 'manager'
    });
    
    if (models.Expense) {
      Project.hasMany(models.Expense, {
        foreignKey: 'projectId',
        as: 'expenses'
      });
    }
    
    if (models.Budget) {
      Project.hasMany(models.Budget, {
        foreignKey: 'projectId',
        as: 'budgets'
      });
    }
  };

  return Project;
};