const { body, query, param, validationResult } = require('express-validator');

const handleValidationErrors = (req, res, next) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
        return res.status(400).json({
            error: 'Validation failed',
            details: errors.array()
        });
    }
    next();
};

// Transaction validation rules
const validateTransaction = [
    body('amount')
        .isNumeric()
        .withMessage('Amount must be a number')
        .custom(value => {
            if (value <= 0) {
                throw new Error('Amount must be greater than 0');
            }
            return true;
        }),
    body('type')
        .isIn(['income', 'expense', 'transfer'])
        .withMessage('Type must be income, expense, or transfer'),
    body('description')
        .trim()
        .isLength({ min: 1, max: 500 })
        .withMessage('Description must be between 1 and 500 characters'),
    body('transaction_date')
        .isISO8601()
        .withMessage('Transaction date must be a valid date'),
    body('account_id')
        .isUUID()
        .withMessage('Account ID must be a valid UUID'),
    handleValidationErrors
];

// Account validation rules
const validateAccount = [
    body('name')
        .trim()
        .isLength({ min: 1, max: 255 })
        .withMessage('Account name must be between 1 and 255 characters'),
    body('type')
        .isIn(['checking', 'savings', 'credit', 'investment', 'cash'])
        .withMessage('Account type must be one of: checking, savings, credit, investment, cash'),
    body('currency')
        .optional()
        .isLength({ min: 3, max: 3 })
        .withMessage('Currency must be 3 characters'),
    handleValidationErrors
];

// Budget validation rules
const validateBudget = [
    body('amount')
        .isNumeric()
        .custom(value => {
            if (value <= 0) {
                throw new Error('Budget amount must be greater than 0');
            }
            return true;
        }),
    body('period')
        .isIn(['weekly', 'monthly', 'yearly'])
        .withMessage('Period must be weekly, monthly, or yearly'),
    body('start_date')
        .isISO8601()
        .withMessage('Start date must be a valid date'),
    body('end_date')
        .isISO8601()
        .withMessage('End date must be a valid date'),
    body('category_id')
        .isUUID()
        .withMessage('Category ID must be a valid UUID'),
    handleValidationErrors
];

// Query parameter validation for transactions
const validateTransactionQuery = [
    query('start_date')
        .optional()
        .isISO8601()
        .withMessage('Start date must be a valid date'),
    query('end_date')
        .optional()
        .isISO8601()
        .withMessage('End date must be a valid date'),
    query('page')
        .optional()
        .isInt({ min: 1 })
        .withMessage('Page must be a positive integer'),
    query('limit')
        .optional()
        .isInt({ min: 1, max: 100 })
        .withMessage('Limit must be between 1 and 100'),
    handleValidationErrors
];

module.exports = {
    validateTransaction,
    validateAccount,
    validateBudget,
    validateTransactionQuery,
    handleValidationErrors
};