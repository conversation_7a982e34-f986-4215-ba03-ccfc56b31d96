'use strict';

/**
 * Custom API Error class for consistent error handling
 */
class ApiError extends Error {
  constructor(message, statusCode, errorCode, errors = []) {
    super(message);
    this.statusCode = statusCode;
    this.errorCode = errorCode;
    this.errors = errors;
    this.name = this.constructor.name;
    Error.captureStackTrace(this, this.constructor);
  }

  static badRequest(message, errorCode = 'BAD_REQUEST', errors = []) {
    return new ApiError(message, 400, errorCode, errors);
  }

  static unauthorized(message = 'Unauthorized', errorCode = 'UNAUTHORIZED') {
    return new ApiError(message, 401, errorCode);
  }

  static forbidden(message = 'Forbidden', errorCode = 'FORBIDDEN') {
    return new ApiError(message, 403, errorCode);
  }

  static notFound(message = 'Resource not found', errorCode = 'NOT_FOUND') {
    return new ApiError(message, 404, errorCode);
  }

  static conflict(message, errorCode = 'CONFLICT') {
    return new ApiError(message, 409, errorCode);
  }

  static validationError(errors) {
    return new ApiError('Validation Error', 400, 'VALIDATION_ERROR', errors);
  }

  static internalError(message = 'Internal Server Error', errorCode = 'INTERNAL_ERROR') {
    return new ApiError(message, 500, errorCode);
  }
}

module.exports = { ApiError };