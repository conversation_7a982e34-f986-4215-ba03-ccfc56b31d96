#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

/**
 * Dependency Checker for server.js
 * Analyzes server.js file and checks if all imported files exist
 * Provides detailed information about missing files and their purpose
 */

class DependencyChecker {
  constructor(serverFilePath = './src/server.js') {
    this.serverFilePath = serverFilePath;
    this.missingFiles = [];
    this.existingFiles = [];
    this.projectRoot = process.cwd();
  }

  /**
   * Extract all require statements from server.js
   */
  extractRequireStatements(content) {
    const requireRegex = /require\(['"`]([^'"`]+)['"`]\)/g;
    const requires = [];
    let match;

    while ((match = requireRegex.exec(content)) !== null) {
      const modulePath = match[1];
      // Skip node_modules and built-in modules
      if (!modulePath.startsWith('.') && !modulePath.startsWith('/')) {
        continue;
      }
      requires.push(modulePath);
    }

    return requires;
  }

  /**
   * Resolve relative path to absolute path
   */
  resolvePath(relativePath, basePath) {
    const serverDir = path.dirname(basePath);
    let resolvedPath = path.resolve(serverDir, relativePath);

    // Try different extensions if file doesn't exist
    const extensions = ['', '.js', '.json', '/index.js'];

    for (const ext of extensions) {
      const testPath = resolvedPath + ext;
      if (fs.existsSync(testPath)) {
        return testPath;
      }
    }

    // Check if it's a directory with index.js
    if (fs.existsSync(resolvedPath) && fs.statSync(resolvedPath).isDirectory()) {
      const indexPath = path.join(resolvedPath, 'index.js');
      if (fs.existsSync(indexPath)) {
        return indexPath;
      }
    }

    // Return the most likely path based on file type
    if (relativePath.endsWith('.json')) {
      return resolvedPath;
    }
    return resolvedPath + '.js'; // Default assumption
  }

  /**
   * Get file purpose based on path and context
   */
  getFilePurpose(filePath) {
    const purposes = {
      // Middleware
      'middleware/errorHandler': 'Error handling middleware for catching and processing application errors',
      
      // Utils
      'utils/logger': 'Logging utility for application logs, errors, and debugging',
      
      // Models
      'models': 'Database models and Sequelize configuration (index.js should export all models)',
      
      // Documentation
      'docs/swagger.json': 'API documentation configuration for Swagger UI',
      
      // Routes
      'api/v1/routes/authRoutes': 'Authentication routes (login, register, logout, password reset)',
      'api/v1/routes/expenseRoutes': 'Expense management routes (CRUD operations for expenses)',
      'api/v1/routes/approvalRoutes': 'Approval workflow routes (approve/reject expenses)',
      'api/v1/routes/reportRoutes': 'Financial reporting routes (generate various reports)',
      'api/v1/routes/bankingRoutes': 'Banking integration routes (account management, transactions)',
      'api/v1/routes/budgetRoutes': 'Budget management routes (create, update, monitor budgets)',
      'api/v1/routes/startupRoutes': 'Startup finance routes (runway tracking, burn rate, projections)',
      'api/v1/routes/dashboardRoutes': 'Dashboard data routes (analytics, charts, KPIs)',
      'api/v1/routes/adminRoutes': 'Admin management routes (user management, system settings)',
      'api/v1/routes/notificationRoutes': 'Notification system routes (send, receive, manage notifications)',
      'api/v1/routes/aiRoutes': 'AI-powered features routes (OCR, categorization, insights)',
      'api/v1/routes/currencyRoutes': 'Currency management routes (exchange rates, conversions)',
      'api/v1/routes/fileRoutes': 'File management routes (upload, download, process files)',
    };

    // Find matching purpose
    for (const [pathPattern, purpose] of Object.entries(purposes)) {
      if (filePath.includes(pathPattern)) {
        return purpose;
      }
    }

    return 'Unknown purpose - please check the import context in server.js';
  }

  /**
   * Get suggested file structure for missing route files
   */
  getRouteFileStructure(routeName) {
    const controllerName = routeName.replace('Routes', 'Controller');
    
    return {
      routeFile: `Basic Express router setup with route definitions`,
      controllerFile: `src/api/v1/controllers/${controllerName}.js - Controller with business logic`,
      validatorFile: `src/api/v1/validators/${routeName.replace('Routes', 'Validator')}.js - Input validation middleware`,
      dependencies: [
        'Express Router',
        'Authentication middleware',
        'Validation middleware',
        'Controller functions'
      ]
    };
  }

  /**
   * Check if file exists
   */
  checkFile(relativePath) {
    const resolvedPath = this.resolvePath(relativePath, this.serverFilePath);
    const exists = fs.existsSync(resolvedPath);
    
    const fileInfo = {
      relativePath,
      resolvedPath,
      exists,
      purpose: this.getFilePurpose(relativePath)
    };

    if (exists) {
      this.existingFiles.push(fileInfo);
    } else {
      this.missingFiles.push(fileInfo);
    }

    return fileInfo;
  }

  /**
   * Main analysis function
   */
  analyze() {
    console.log('🔍 Analyzing server.js dependencies...\n');

    if (!fs.existsSync(this.serverFilePath)) {
      console.error(`❌ Server file not found: ${this.serverFilePath}`);
      return;
    }

    const content = fs.readFileSync(this.serverFilePath, 'utf8');
    const requires = this.extractRequireStatements(content);

    console.log(`📁 Found ${requires.length} local file imports in server.js\n`);

    // Check each required file
    requires.forEach(req => this.checkFile(req));

    this.generateReport();
  }

  /**
   * Generate detailed report
   */
  generateReport() {
    console.log('📊 DEPENDENCY ANALYSIS REPORT');
    console.log('=' .repeat(50));
    
    // Summary
    console.log(`\n📈 SUMMARY:`);
    console.log(`   ✅ Existing files: ${this.existingFiles.length}`);
    console.log(`   ❌ Missing files: ${this.missingFiles.length}`);
    console.log(`   📊 Total dependencies: ${this.existingFiles.length + this.missingFiles.length}`);

    if (this.missingFiles.length === 0) {
      console.log('\n🎉 All dependencies are satisfied! No missing files found.');
      return;
    }

    // Missing files details
    console.log(`\n❌ MISSING FILES (${this.missingFiles.length}):`);
    console.log('-'.repeat(50));
    
    this.missingFiles.forEach((file, index) => {
      console.log(`\n${index + 1}. ${file.relativePath}`);
      console.log(`   📍 Expected location: ${file.resolvedPath}`);
      console.log(`   🎯 Purpose: ${file.purpose}`);
      
      // Add specific suggestions for route files
      if (file.relativePath.includes('Routes')) {
        const routeName = path.basename(file.relativePath, '.js');
        const structure = this.getRouteFileStructure(routeName);
        console.log(`   📋 Required structure:`);
        console.log(`      • ${file.resolvedPath} - ${structure.routeFile}`);
        console.log(`      • ${structure.controllerFile}`);
        console.log(`      • ${structure.validatorFile}`);
        console.log(`   🔧 Dependencies: ${structure.dependencies.join(', ')}`);
      }
    });

    // Existing files (brief)
    if (this.existingFiles.length > 0) {
      console.log(`\n✅ EXISTING FILES (${this.existingFiles.length}):`);
      console.log('-'.repeat(30));
      this.existingFiles.forEach(file => {
        console.log(`   ✓ ${file.relativePath}`);
      });
    }

    // Action items
    console.log('\n🚀 RECOMMENDED ACTIONS:');
    console.log('-'.repeat(30));
    
    const missingRoutes = this.missingFiles.filter(f => f.relativePath.includes('Routes'));
    const missingOther = this.missingFiles.filter(f => !f.relativePath.includes('Routes'));
    
    if (missingRoutes.length > 0) {
      console.log('\n1. Create missing route files:');
      missingRoutes.forEach(file => {
        console.log(`   • ${file.resolvedPath}`);
      });
    }
    
    if (missingOther.length > 0) {
      console.log('\n2. Create missing utility/config files:');
      missingOther.forEach(file => {
        console.log(`   • ${file.resolvedPath}`);
      });
    }

    console.log('\n3. For each missing route file, also create:');
    console.log('   • Corresponding controller in src/api/v1/controllers/');
    console.log('   • Validation middleware in src/api/v1/validators/');
    console.log('   • Unit tests in tests/ directory');

    console.log('\n💡 TIP: Run this script again after creating files to verify all dependencies are resolved.');

    // Generate file templates
    if (this.missingFiles.length > 0) {
      this.generateFileTemplates();
    }
  }

  /**
   * Generate basic file templates for missing files
   */
  generateFileTemplates() {
    console.log('\n📝 FILE TEMPLATES:');
    console.log('='.repeat(50));

    this.missingFiles.forEach((file, index) => {
      if (file.relativePath.includes('Routes')) {
        this.generateRouteTemplate(file);
      }
    });
  }

  /**
   * Generate route file template
   */
  generateRouteTemplate(file) {
    const routeName = path.basename(file.relativePath, '.js');
    const controllerName = routeName.replace('Routes', 'Controller');
    const entityName = routeName.replace('Routes', '').toLowerCase();

    console.log(`\n📄 Template for ${file.relativePath}:`);
    console.log('-'.repeat(30));
    console.log(`
const express = require('express');
const router = express.Router();
const ${controllerName} = require('../controllers/${controllerName}');
const auth = require('../../../middleware/auth');
const { validate${routeName.replace('Routes', '')} } = require('../validators/${routeName.replace('Routes', 'Validator')}');

// GET /${entityName}s - Get all ${entityName}s
router.get('/', auth, ${controllerName}.getAll);

// GET /${entityName}s/:id - Get ${entityName} by ID
router.get('/:id', auth, ${controllerName}.getById);

// POST /${entityName}s - Create new ${entityName}
router.post('/', auth, validate${routeName.replace('Routes', '')}, ${controllerName}.create);

// PUT /${entityName}s/:id - Update ${entityName}
router.put('/:id', auth, validate${routeName.replace('Routes', '')}, ${controllerName}.update);

// DELETE /${entityName}s/:id - Delete ${entityName}
router.delete('/:id', auth, ${controllerName}.delete);

module.exports = router;
    `);

    console.log(`📄 Required Controller: src/api/v1/controllers/${controllerName}.js`);
    console.log(`📄 Required Validator: src/api/v1/validators/${routeName.replace('Routes', 'Validator')}.js`);
  }
}

// Run the analysis
if (require.main === module) {
  const checker = new DependencyChecker();
  checker.analyze();
}

module.exports = DependencyChecker;
