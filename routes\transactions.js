const express = require('express');
const { authenticateToken } = require('../middleware/auth');
const { validateTransaction, validateTransactionQuery } = require('../middleware/validation');
const { pool } = require('../config/database');
const analyticsService = require('../services/analyticsService');
const exportService = require('../services/exportService');
const logger = require('../utils/logger');

const router = express.Router();

// Get transactions with advanced filtering
router.get('/', authenticateToken, validateTransactionQuery, async (req, res, next) => {
    try {
        const userId = req.userId;
        const {
            start_date,
            end_date,
            category_id,
            account_id,
            type,
            min_amount,
            max_amount,
            tags,
            merchant,
            page = 1,
            limit = 50
        } = req.query;

        const { query, params } = buildFilterQuery({
            userId,
            start_date,
            end_date,
            category_id,
            account_id,
            type,
            min_amount,
            max_amount,
            tags: tags ? tags.split(',') : null,
            merchant
        });

        // Add pagination
        const offset = (page - 1) * limit;
        const paginatedQuery = `${query} ORDER BY transaction_date DESC, created_at DESC LIMIT $${params.length + 1} OFFSET $${params.length + 2}`;
        params.push(limit, offset);

        // Get transactions
        const result = await pool.query(paginatedQuery, params);

        // Get total count for pagination
        const countQuery = query.replace('SELECT t.*, c.name as category_name, a.name as account_name', 'SELECT COUNT(*)');
        const countResult = await pool.query(countQuery, params.slice(0, -2));
        const totalCount = parseInt(countResult.rows[0].count);

        res.json({
            transactions: result.rows,
            pagination: {
                page: parseInt(page),
                limit: parseInt(limit),
                total: totalCount,
                pages: Math.ceil(totalCount / limit)
            }
        });
    } catch (error) {
        next(error);
    }
});

// Get calendar view data
router.get('/calendar/:year/:month', authenticateToken, async (req, res, next) => {
    try {
        const userId = req.userId;
        const { year, month } = req.params;

        // Validate year and month
        const yearInt = parseInt(year);
        const monthInt = parseInt(month);
        
        if (yearInt < 1900 || yearInt > 2100 || monthInt < 1 || monthInt > 12) {
            return res.status(400).json({ error: 'Invalid year or month' });
        }

        const startDate = new Date(yearInt, monthInt - 1, 1);
        const endDate = new Date(yearInt, monthInt, 0);

        const query = `
            SELECT 
                t.*,
                c.name as category_name,
                a.name as account_name
            FROM transactions t
            LEFT JOIN categories c ON t.category_id = c.id
            LEFT JOIN accounts a ON t.account_id = a.id
            WHERE t.user_id = $1 
            AND t.transaction_date BETWEEN $2 AND $3
            ORDER BY t.transaction_date ASC
        `;

        const result = await pool.query(query, [userId, startDate, endDate]);
        const transactions = result.rows;

        // Group transactions by day
        const calendarData = {};
        transactions.forEach(transaction => {
            const day = transaction.transaction_date.getDate();
            if (!calendarData[day]) {
                calendarData[day] = {
                    income: 0,
                    expenses: 0,
                    transactions: []
                };
            }

            const amount = parseFloat(transaction.amount);
            if (transaction.type === 'income') {
                calendarData[day].income += amount;
            } else if (transaction.type === 'expense') {
                calendarData[day].expenses += amount;
            }

            calendarData[day].transactions.push(transaction);
        });

        res.json({
            year: yearInt,
            month: monthInt,
            calendar_data: calendarData
        });
    } catch (error) {
        next(error);
    }
});

// Create transaction
router.post('/', authenticateToken, validateTransaction, async (req, res, next) => {
    try {
        const userId = req.userId;
        const {
            account_id,
            category_id,
            amount,
            type,
            description,
            transaction_date,
            tags,
            merchant,
            location,
            receipt_url
        } = req.body;

        // Verify account belongs to user
        const accountCheck = await pool.query(
            'SELECT id FROM accounts WHERE id = $1 AND user_id = $2',
            [account_id, userId]
        );

        if (accountCheck.rows.length === 0) {
            return res.status(404).json({ error: 'Account not found' });
        }

        // Insert transaction
        const result = await pool.query(`
            INSERT INTO transactions (
                user_id, account_id, category_id, amount, type, description, 
                transaction_date, tags, merchant, location, receipt_url
            ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11)
            RETURNING *
        `, [
            userId, account_id, category_id, amount, type, description,
            transaction_date, tags, merchant, location, receipt_url
        ]);

        const transaction = result.rows[0];

        // Update account balance
        const balanceChange = type === 'income' ? amount : -amount;
        await pool.query(
            'UPDATE accounts SET balance = balance + $1 WHERE id = $2',
            [balanceChange, account_id]
        );

        logger.info(`Transaction created: ${transaction.id} for user: ${userId}`);

        res.status(201).json({
            message: 'Transaction created successfully',
            transaction
        });
    } catch (error) {
        next(error);
    }
});

// Update transaction
router.put('/:id', authenticateToken, validateTransaction, async (req, res, next) => {
    try {
        const userId = req.userId;
        const transactionId = req.params.id;
        const {
            account_id,
            category_id,
            amount,
            type,
            description,
            transaction_date,
            tags,
            merchant,
            location,
            receipt_url
        } = req.body;

        // Get original transaction
        const originalResult = await pool.query(
            'SELECT * FROM transactions WHERE id = $1 AND user_id = $2',
            [transactionId, userId]
        );

        if (originalResult.rows.length === 0) {
            return res.status(404).json({ error: 'Transaction not found' });
        }

        const originalTransaction = originalResult.rows[0];

        // Verify new account belongs to user
        const accountCheck = await pool.query(
            'SELECT id FROM accounts WHERE id = $1 AND user_id = $2',
            [account_id, userId]
        );

        if (accountCheck.rows.length === 0) {
            return res.status(404).json({ error: 'Account not found' });
        }

        // Update transaction
        const result = await pool.query(`
            UPDATE transactions SET
                account_id = $3,
                category_id = $4,
                amount = $5,
                type = $6,
                description = $7,
                transaction_date = $8,
                tags = $9,
                merchant = $10,
                location = $11,
                receipt_url = $12
            WHERE id = $1 AND user_id = $2
            RETURNING *
        `, [
            transactionId, userId, account_id, category_id, amount, type,
            description, transaction_date, tags, merchant, location, receipt_url
        ]);

        const updatedTransaction = result.rows[0];

        // Update account balances
        // Reverse original transaction
        const originalBalanceChange = originalTransaction.type === 'income' 
            ? -originalTransaction.amount 
            : originalTransaction.amount;
        
        await pool.query(
            'UPDATE accounts SET balance = balance + $1 WHERE id = $2',
            [originalBalanceChange, originalTransaction.account_id]
        );

        // Apply new transaction
        const newBalanceChange = type === 'income' ? amount : -amount;
        await pool.query(
            'UPDATE accounts SET balance = balance + $1 WHERE id = $2',
            [newBalanceChange, account_id]
        );

        logger.info(`Transaction updated: ${transactionId} for user: ${userId}`);

        res.json({
            message: 'Transaction updated successfully',
            transaction: updatedTransaction
        });
    } catch (error) {
        next(error);
    }
});

// Delete transaction
router.delete('/:id', authenticateToken, async (req, res, next) => {
    try {
        const userId = req.userId;
        const transactionId = req.params.id;

        // Get transaction to reverse balance change
        const result = await pool.query(
            'SELECT * FROM transactions WHERE id = $1 AND user_id = $2',
            [transactionId, userId]
        );

        if (result.rows.length === 0) {
            return res.status(404).json({ error: 'Transaction not found' });
        }

        const transaction = result.rows[0];

        // Delete transaction
        await pool.query(
            'DELETE FROM transactions WHERE id = $1 AND user_id = $2',
            [transactionId, userId]
        );

        // Reverse balance change
        const balanceChange = transaction.type === 'income' 
            ? -transaction.amount 
            : transaction.amount;
        
        await pool.query(
            'UPDATE accounts SET balance = balance + $1 WHERE id = $2',
            [balanceChange, transaction.account_id]
        );

        logger.info(`Transaction deleted: ${transactionId} for user: ${userId}`);

        res.json({ message: 'Transaction deleted successfully' });
    } catch (error) {
        next(error);
    }
});

// Bulk import transactions
router.post('/bulk-import', authenticateToken, async (req, res, next) => {
    try {
        const userId = req.userId;
        const { transactions } = req.body;

        if (!Array.isArray(transactions) || transactions.length === 0) {
            return res.status(400).json({ error: 'Transactions array is required' });
        }

        if (transactions.length > 1000) {
            return res.status(400).json({ error: 'Maximum 1000 transactions per batch' });
        }

        const client = await pool.connect();
        try {
            await client.query('BEGIN');

            const results = [];
            for (const transaction of transactions) {
                // Validate each transaction
                const {
                    account_id,
                    category_id,
                    amount,
                    type,
                    description,
                    transaction_date,
                    tags,
                    merchant,
                    location
                } = transaction;

                // Verify account belongs to user
                const accountCheck = await client.query(
                    'SELECT id FROM accounts WHERE id = $1 AND user_id = $2',
                    [account_id, userId]
                );

                if (accountCheck.rows.length === 0) {
                    throw new Error(`Account ${account_id} not found`);
                }

                // Insert transaction
                const result = await client.query(`
                    INSERT INTO transactions (
                        user_id, account_id, category_id, amount, type, description,
                        transaction_date, tags, merchant, location
                    ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)
                    RETURNING *
                `, [
                    userId, account_id, category_id, amount, type, description,
                    transaction_date, tags, merchant, location
                ]);

                results.push(result.rows[0]);

                // Update account balance
                const balanceChange = type === 'income' ? amount : -amount;
                await client.query(
                    'UPDATE accounts SET balance = balance + $1 WHERE id = $2',
                    [balanceChange, account_id]
                );
            }

            await client.query('COMMIT');

            logger.info(`Bulk import completed: ${results.length} transactions for user: ${userId}`);

            res.status(201).json({
                message: 'Transactions imported successfully',
                imported_count: results.length,
                transactions: results
            });
        } catch (error) {
            await client.query('ROLLBACK');
            throw error;
        } finally {
            client.release();
        }
    } catch (error) {
        next(error);
    }
});

// Export transactions
router.get('/export', authenticateToken, async (req, res, next) => {
    try {
        const userId = req.userId;
        const { format = 'excel', ...filters } = req.query;

        if (format !== 'excel') {
            return res.status(400).json({ error: 'Only Excel format is currently supported' });
        }

        const buffer = await exportService.exportTransactionsToExcel(userId, filters);

        const filename = `transactions_${new Date().toISOString().split('T')[0]}.xlsx`;
        
        res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        res.setHeader('Content-Disposition', `attachment; filename="${filename}"`);
        
        res.send(buffer);
    } catch (error) {
        next(error);
    }
});

// Helper function to build filter query
function buildFilterQuery(filters) {
    let query = `
        SELECT 
            t.*,
            c.name as category_name,
            a.name as account_name
        FROM transactions t
        LEFT JOIN categories c ON t.category_id = c.id
        LEFT JOIN accounts a ON t.account_id = a.id
        WHERE t.user_id = $1
    `;
    
    const params = [filters.userId];
    let paramIndex = 2;

    if (filters.start_date && filters.end_date) {
        query += ` AND t.transaction_date BETWEEN $${paramIndex} AND $${paramIndex + 1}`;
        params.push(filters.start_date, filters.end_date);
        paramIndex += 2;
    }

    if (filters.category_id) {
        query += ` AND t.category_id = $${paramIndex}`;
        params.push(filters.category_id);
        paramIndex++;
    }

    if (filters.account_id) {
        query += ` AND t.account_id = $${paramIndex}`;
        params.push(filters.account_id);
        paramIndex++;
    }

    if (filters.type) {
        query += ` AND t.type = $${paramIndex}`;
        params.push(filters.type);
        paramIndex++;
    }

    if (filters.min_amount !== undefined) {
        query += ` AND t.amount >= $${paramIndex}`;
        params.push(parseFloat(filters.min_amount));
        paramIndex++;
    }

    if (filters.max_amount !== undefined) {
        query += ` AND t.amount <= $${paramIndex}`;
        params.push(parseFloat(filters.max_amount));
        paramIndex++;
    }

    if (filters.tags && filters.tags.length > 0) {
        query += ` AND t.tags && $${paramIndex}`;
        params.push(filters.tags);
        paramIndex++;
    }

    if (filters.merchant) {
        query += ` AND t.merchant ILIKE $${paramIndex}`;
        params.push(`%${filters.merchant}%`);
        paramIndex++;
    }

    return { query, params };
}

module.exports = router;