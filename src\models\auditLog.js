'use strict';

module.exports = (sequelize, DataTypes) => {
  const AuditLog = sequelize.define('AuditLog', {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true
    },
    entityType: {
      type: DataTypes.STRING(50),
      allowNull: false
    },
    entityId: {
      type: DataTypes.UUID,
      allowNull: false
    },
    action: {
      type: DataTypes.STRING(50),
      allowNull: false
    },
    changes: {
      type: DataTypes.JSONB,
      defaultValue: {}
    },
    userId: {
      type: DataTypes.UUID,
      allowNull: false,
      references: {
        model: 'Users',
        key: 'id'
      }
    },
    ipAddress: {
      type: DataTypes.INET
    },
    userAgent: {
      type: DataTypes.TEXT
    },
    sessionId: {
      type: DataTypes.STRING
    }
  }, {
    timestamps: true,
    indexes: [
      { fields: ['entityType', 'entityId'] },
      { fields: ['userId'] },
      { fields: ['action'] },
      { fields: ['createdAt'] }
    ]
  });

  AuditLog.associate = (models) => {
    AuditLog.belongsTo(models.User, {
      foreignKey: 'userId',
      as: 'user'
    });
  };

  return AuditLog;
};