'use strict';

module.exports = (sequelize, DataTypes) => {
  const RunwayAlert = sequelize.define('RunwayAlert', {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true
    },
    companyId: {
      type: DataTypes.UUID,
      allowNull: false,
      references: {
        model: 'Companies',
        key: 'id'
      }
    },
    alertType: {
      type: DataTypes.ENUM('runway_warning', 'burn_spike', 'cash_low', 'critical_runway'),
      allowNull: false
    },
    message: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    thresholdValue: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: false
    },
    currentValue: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: false
    },
    isActive: {
      type: DataTypes.BOOLEAN,
      defaultValue: true
    },
    severity: {
      type: DataTypes.ENUM('low', 'medium', 'high', 'critical'),
      defaultValue: 'medium'
    },
    dismissedAt: {
      type: DataTypes.DATE
    },
    dismissedBy: {
      type: DataTypes.UUID,
      references: {
        model: 'Users',
        key: 'id'
      }
    }
  }, {
    timestamps: true,
    indexes: [
      { fields: ['companyId'] },
      { fields: ['alertType'] },
      { fields: ['isActive'] },
      { fields: ['severity'] }
    ]
  });

  RunwayAlert.associate = (models) => {
    RunwayAlert.belongsTo(models.Company, {
      foreignKey: 'companyId',
      as: 'company'
    });
    
    RunwayAlert.belongsTo(models.User, {
      foreignKey: 'dismissedBy',
      as: 'dismisser'
    });
  };

  return RunwayAlert;
};