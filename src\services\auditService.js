'use strict';

const { AuditLog } = require('../models');
const logger = require('../utils/logger');
const { ApiError } = require('../utils/apiError');

/**
 * Service for audit logging and tracking
 */
class AuditService {
  
  /**
   * Log an audit event
   * @param {Object} auditData - Audit event data
   * @returns {Promise<Object>} Created audit log
   */
  async logEvent(auditData) {
    try {
      const {
        entityType,
        entityId,
        action,
        changes = {},
        userId,
        ipAddress,
        userAgent,
        sessionId
      } = auditData;
      
      const auditLog = await AuditLog.create({
        entityType,
        entityId,
        action,
        changes,
        userId,
        ipAddress,
        userAgent,
        sessionId
      });
      
      return auditLog;
    } catch (error) {
      logger.error('Error creating audit log:', error);
      throw new ApiError('Failed to create audit log', 500);
    }
  }
  
  /**
   * Get audit logs with filtering
   * @param {Object} filters - Filter options
   * @returns {Promise<Object>} Audit logs with pagination
   */
  async getAuditLogs(filters = {}) {
    try {
      const {
        page = 1,
        limit = 50,
        entityType,
        entityId,
        action,
        userId,
        startDate,
        endDate
      } = filters;
      
      const where = {};
      
      if (entityType) where.entityType = entityType;
      if (entityId) where.entityId = entityId;
      if (action) where.action = action;
      if (userId) where.userId = userId;
      
      if (startDate || endDate) {
        where.createdAt = {};
        if (startDate) where.createdAt[Op.gte] = new Date(startDate);
        if (endDate) where.createdAt[Op.lte] = new Date(endDate);
      }
      
      const offset = (page - 1) * limit;
      
      const { count, rows } = await AuditLog.findAndCountAll({
        where,
        include: [{
          model: require('../models').User,
          as: 'user',
          attributes: ['firstName', 'lastName', 'email']
        }],
        order: [['createdAt', 'DESC']],
        limit: parseInt(limit),
        offset
      });
      
      return {
        logs: rows,
        pagination: {
          total: count,
          page: parseInt(page),
          limit: parseInt(limit),
          totalPages: Math.ceil(count / limit)
        }
      };
    } catch (error) {
      logger.error('Error getting audit logs:', error);
      throw new ApiError('Failed to get audit logs', 500);
    }
  }
  
  /**
   * Get audit trail for a specific entity
   * @param {string} entityType - Entity type
   * @param {string} entityId - Entity ID
   * @returns {Promise<Array>} Audit trail
   */
  async getEntityAuditTrail(entityType, entityId) {
    try {
      const logs = await AuditLog.findAll({
        where: { entityType, entityId },
        include: [{
          model: require('../models').User,
          as: 'user',
          attributes: ['firstName', 'lastName', 'email']
        }],
        order: [['createdAt', 'ASC']]
      });
      
      return logs;
    } catch (error) {
      logger.error('Error getting entity audit trail:', error);
      throw new ApiError('Failed to get audit trail', 500);
    }
  }
  
  /**
   * Get user activity logs
   * @param {string} userId - User ID
   * @param {Object} options - Query options
   * @returns {Promise<Object>} User activity logs
   */
  async getUserActivity(userId, options = {}) {
    try {
      const {
        page = 1,
        limit = 50,
        startDate,
        endDate,
        action
      } = options;
      
      const where = { userId };
      
      if (action) where.action = action;
      
      if (startDate || endDate) {
        where.createdAt = {};
        if (startDate) where.createdAt[Op.gte] = new Date(startDate);
        if (endDate) where.createdAt[Op.lte] = new Date(endDate);
      }
      
      const offset = (page - 1) * limit;
      
      const { count, rows } = await AuditLog.findAndCountAll({
        where,
        order: [['createdAt', 'DESC']],
        limit: parseInt(limit),
        offset
      });
      
      return {
        activities: rows,
        pagination: {
          total: count,
          page: parseInt(page),
          limit: parseInt(limit),
          totalPages: Math.ceil(count / limit)
        }
      };
    } catch (error) {
      logger.error('Error getting user activity:', error);
      throw new ApiError('Failed to get user activity', 500);
    }
  }
  
  /**
   * Generate audit report
   * @param {Object} reportOptions - Report options
   * @returns {Promise<Object>} Audit report data
   */
  async generateAuditReport(reportOptions = {}) {
    try {
      const {
        startDate,
        endDate,
        entityTypes = [],
        actions = [],
        userIds = []
      } = reportOptions;
      
      const where = {};
      
      if (entityTypes.length > 0) where.entityType = { [Op.in]: entityTypes };
      if (actions.length > 0) where.action = { [Op.in]: actions };
      if (userIds.length > 0) where.userId = { [Op.in]: userIds };
      
      if (startDate || endDate) {
        where.createdAt = {};
        if (startDate) where.createdAt[Op.gte] = new Date(startDate);
        if (endDate) where.createdAt[Op.lte] = new Date(endDate);
      }
      
      const [logs, summary] = await Promise.all([
        AuditLog.findAll({
          where,
          include: [{
            model: require('../models').User,
            as: 'user',
            attributes: ['firstName', 'lastName', 'email']
          }],
          order: [['createdAt', 'DESC']]
        }),
        this._generateAuditSummary(where)
      ]);
      
      return {
        logs,
        summary,
        reportGenerated: new Date(),
        filters: reportOptions
      };
    } catch (error) {
      logger.error('Error generating audit report:', error);
      throw new ApiError('Failed to generate audit report', 500);
    }
  }
  
  /**
   * Log expense-related audit events
   * @param {Object} expense - Expense object
   * @param {string} action - Action performed
   * @param {Object} changes - Changes made
   * @param {Object} user - User performing action
   * @param {Object} request - Request object for IP/user agent
   * @returns {Promise<void>}
   */
  async logExpenseEvent(expense, action, changes, user, request) {
    try {
      await this.logEvent({
        entityType: 'expense',
        entityId: expense.id,
        action,
        changes,
        userId: user.id,
        ipAddress: request.ip,
        userAgent: request.get('User-Agent'),
        sessionId: request.sessionId
      });
    } catch (error) {
      logger.error('Error logging expense event:', error);
      // Don't throw to avoid breaking main flow
    }
  }
  
  /**
   * Log approval-related audit events
   * @param {Object} approval - Approval object
   * @param {string} action - Action performed
   * @param {Object} changes - Changes made
   * @param {Object} user - User performing action
   * @param {Object} request - Request object
   * @returns {Promise<void>}
   */
  async logApprovalEvent(approval, action, changes, user, request) {
    try {
      await this.logEvent({
        entityType: 'approval',
        entityId: approval.id,
        action,
        changes,
        userId: user.id,
        ipAddress: request.ip,
        userAgent: request.get('User-Agent'),
        sessionId: request.sessionId
      });
    } catch (error) {
      logger.error('Error logging approval event:', error);
    }
  }
  
  /**
   * Generate audit summary statistics
   * @param {Object} where - Where clause for filtering
   * @returns {Promise<Object>} Summary statistics
   * @private
   */
  async _generateAuditSummary(where) {
    try {
      const { Op } = require('sequelize');
      
      const [
        totalEvents,
        eventsByType,
        eventsByAction,
        eventsByUser,
        recentActivity
      ] = await Promise.all([
        AuditLog.count({ where }),
        AuditLog.findAll({
          where,
          attributes: [
            'entityType',
            [AuditLog.sequelize.fn('COUNT', '*'), 'count']
          ],
          group: ['entityType'],
          raw: true
        }),
        AuditLog.findAll({
          where,
          attributes: [
            'action',
            [AuditLog.sequelize.fn('COUNT', '*'), 'count']
          ],
          group: ['action'],
          raw: true
        }),
        AuditLog.findAll({
          where,
          attributes: [
            'userId',
            [AuditLog.sequelize.fn('COUNT', '*'), 'count']
          ],
          group: ['userId'],
          order: [[AuditLog.sequelize.fn('COUNT', '*'), 'DESC']],
          limit: 10,
          raw: true
        }),
        AuditLog.findAll({
          where: {
            ...where,
            createdAt: {
              [Op.gte]: new Date(Date.now() - 24 * 60 * 60 * 1000) // Last 24 hours
            }
          },
          attributes: [
            [AuditLog.sequelize.fn('DATE_TRUNC', 'hour', AuditLog.sequelize.col('createdAt')), 'hour'],
            [AuditLog.sequelize.fn('COUNT', '*'), 'count']
          ],
          group: [AuditLog.sequelize.fn('DATE_TRUNC', 'hour', AuditLog.sequelize.col('createdAt'))],
          order: [[AuditLog.sequelize.fn('DATE_TRUNC', 'hour', AuditLog.sequelize.col('createdAt')), 'ASC']],
          raw: true
        })
      ]);
      
      return {
        totalEvents,
        eventsByType: eventsByType.reduce((acc, item) => {
          acc[item.entityType] = parseInt(item.count);
          return acc;
        }, {}),
        eventsByAction: eventsByAction.reduce((acc, item) => {
          acc[item.action] = parseInt(item.count);
          return acc;
        }, {}),
        topUsers: eventsByUser.map(item => ({
          userId: item.userId,
          eventCount: parseInt(item.count)
        })),
        recentActivity: recentActivity.map(item => ({
          hour: item.hour,
          count: parseInt(item.count)
        }))
      };
    } catch (error) {
      logger.error('Error generating audit summary:', error);
      return {};
    }
  }
}

module.exports = new AuditService();