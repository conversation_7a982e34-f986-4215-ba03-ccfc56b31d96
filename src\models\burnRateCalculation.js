'use strict';

module.exports = (sequelize, DataTypes) => {
  const BurnRateCalculation = sequelize.define('BurnRateCalculation', {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true
    },
    companyId: {
      type: DataTypes.UUID,
      allowNull: false,
      references: {
        model: 'Companies',
        key: 'id'
      }
    },
    month: {
      type: DataTypes.DATEONLY,
      allowNull: false
    },
    totalExpenses: {
      type: DataTypes.DECIMAL(15, 2),
      allowNull: false,
      defaultValue: 0
    },
    totalRevenue: {
      type: DataTypes.DECIMAL(15, 2),
      allowNull: false,
      defaultValue: 0
    },
    netBurn: {
      type: DataTypes.DECIMAL(15, 2),
      allowNull: false
    },
    runwayMonths: {
      type: DataTypes.INTEGER,
      allowNull: false
    },
    isAutomated: {
      type: DataTypes.BOOLEAN,
      defaultValue: true
    },
    currency: {
      type: DataTypes.STRING,
      defaultValue: 'USD'
    },
    calculationMethod: {
      type: DataTypes.STRING,
      defaultValue: 'expense_based'
    }
  }, {
    timestamps: true,
    indexes: [
      { fields: ['companyId'] },
      { fields: ['month'] },
      { fields: ['companyId', 'month'], unique: true }
    ]
  });

  BurnRateCalculation.associate = (models) => {
    BurnRateCalculation.belongsTo(models.Company, {
      foreignKey: 'companyId',
      as: 'company'
    });
  };

  return BurnRateCalculation;
};