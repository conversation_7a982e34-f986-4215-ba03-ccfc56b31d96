'use strict';

module.exports = (sequelize, DataTypes) => {
  const RecurringExpense = sequelize.define('RecurringExpense', {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true
    },
    companyId: {
      type: DataTypes.UUID,
      allowNull: false,
      references: {
        model: 'Companies',
        key: 'id'
      }
    },
    name: {
      type: DataTypes.STRING(200),
      allowNull: false
    },
    description: {
      type: DataTypes.TEXT
    },
    amount: {
      type: DataTypes.DECIMAL(19, 4),
      allowNull: false
    },
    currency: {
      type: DataTypes.STRING,
      defaultValue: 'USD'
    },
    categoryId: {
      type: DataTypes.UUID,
      allowNull: false,
      references: {
        model: 'Categories',
        key: 'id'
      }
    },
    departmentId: {
      type: DataTypes.UUID,
      references: {
        model: 'Departments',
        key: 'id'
      }
    },
    frequency: {
      type: DataTypes.ENUM('daily', 'weekly', 'monthly', 'quarterly', 'yearly'),
      allowNull: false
    },
    startDate: {
      type: DataTypes.DATEONLY,
      allowNull: false
    },
    endDate: {
      type: DataTypes.DATEONLY
    },
    nextDue: {
      type: DataTypes.DATEONLY,
      allowNull: false
    },
    isActive: {
      type: DataTypes.BOOLEAN,
      defaultValue: true
    },
    autoApprove: {
      type: DataTypes.BOOLEAN,
      defaultValue: false
    },
    createdBy: {
      type: DataTypes.UUID,
      allowNull: false,
      references: {
        model: 'Users',
        key: 'id'
      }
    },
    lastGenerated: {
      type: DataTypes.DATEONLY
    },
    totalGenerated: {
      type: DataTypes.INTEGER,
      defaultValue: 0
    }
  }, {
    timestamps: true,
    indexes: [
      { fields: ['companyId'] },
      { fields: ['categoryId'] },
      { fields: ['nextDue'] },
      { fields: ['isActive'] }
    ]
  });

  RecurringExpense.associate = (models) => {
    RecurringExpense.belongsTo(models.Company, {
      foreignKey: 'companyId',
      as: 'company'
    });
    
    RecurringExpense.belongsTo(models.Category, {
      foreignKey: 'categoryId',
      as: 'category'
    });
    
    RecurringExpense.belongsTo(models.Department, {
      foreignKey: 'departmentId',
      as: 'department'
    });
    
    RecurringExpense.belongsTo(models.User, {
      foreignKey: 'createdBy',
      as: 'creator'
    });
  };

  return RecurringExpense;
};