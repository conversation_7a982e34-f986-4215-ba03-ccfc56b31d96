'use strict';

const currencyService = require('../../../services/currencyService');
const { ApiError } = require('../../../utils/apiError');
const logger = require('../../../utils/logger');

/**
 * Get current exchange rates
 */
const getCurrentRates = async (req, res, next) => {
  try {
    const { base = 'USD' } = req.query;
    
    const rates = await currencyService.getCurrentRates(base);
    
    res.status(200).json({
      status: 'success',
      data: rates
    });
  } catch (error) {
    logger.error('Error getting current rates:', error);
    next(error);
  }
};

/**
 * Get supported currencies
 */
const getSupportedCurrencies = async (req, res, next) => {
  try {
    const currencies = await currencyService.getSupportedCurrencies();
    
    res.status(200).json({
      status: 'success',
      data: currencies
    });
  } catch (error) {
    logger.error('Error getting supported currencies:', error);
    next(error);
  }
};

/**
 * Convert currency
 */
const convertCurrency = async (req, res, next) => {
  try {
    const { amount, from, to, date } = req.body;
    
    if (!amount || !from || !to) {
      throw ApiError.badRequest('Amount, from currency, and to currency are required');
    }
    
    const conversionDate = date ? new Date(date) : null;
    const result = await currencyService.convertCurrency(
      parseFloat(amount), 
      from, 
      to, 
      conversionDate
    );
    
    res.status(200).json({
      status: 'success',
      data: result
    });
  } catch (error) {
    logger.error('Error converting currency:', error);
    next(error);
  }
};

/**
 * Get historical exchange rates
 */
const getHistoricalRates = async (req, res, next) => {
  try {
    const { from, to, startDate, endDate } = req.query;
    
    if (!from || !to || !startDate || !endDate) {
      throw ApiError.badRequest('From currency, to currency, start date, and end date are required');
    }
    
    const rates = await currencyService.getHistoricalRates(
      from,
      to,
      new Date(startDate),
      new Date(endDate)
    );
    
    res.status(200).json({
      status: 'success',
      data: rates
    });
  } catch (error) {
    logger.error('Error getting historical rates:', error);
    next(error);
  }
};

module.exports = {
  getCurrentRates,
  getSupportedCurrencies,
  convertCurrency,
  getHistoricalRates
};