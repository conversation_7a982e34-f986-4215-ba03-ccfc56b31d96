'use strict';

module.exports = (sequelize, DataTypes) => {
  const Budget = sequelize.define('Budget', {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true
    },
    companyId: {
      type: DataTypes.UUID,
      allowNull: false,
      references: {
        model: 'Companies',
        key: 'id'
      }
    },
    name: {
      type: DataTypes.STRING,
      allowNull: false
    },
    description: {
      type: DataTypes.TEXT
    },
    amount: {
      type: DataTypes.DECIMAL(19, 4),
      allowNull: false
    },
    currency: {
      type: DataTypes.STRING,
      defaultValue: 'USD'
    },
    startDate: {
      type: DataTypes.DATEONLY,
      allowNull: false
    },
    endDate: {
      type: DataTypes.DATEONLY,
      allowNull: false
    },
    departmentId: {
      type: DataTypes.UUID,
      references: {
        model: 'Departments',
        key: 'id'
      }
    },
    categoryId: {
      type: DataTypes.UUID,
      references: {
        model: 'Categories',
        key: 'id'
      }
    },
    projectId: {
      type: DataTypes.UUID,
      references: {
        model: 'Projects',
        key: 'id'
      }
    },
    isActive: {
      type: DataTypes.BOOLEAN,
      defaultValue: true
    },
    period: {
      type: DataTypes.ENUM('monthly', 'quarterly', 'annual', 'custom'),
      defaultValue: 'monthly'
    },
    status: {
      type: DataTypes.ENUM('draft', 'active', 'closed'),
      defaultValue: 'draft'
    },
    actualAmount: {
      type: DataTypes.DECIMAL(19, 4),
      defaultValue: 0
    },
    variance: {
      type: DataTypes.DECIMAL(19, 4),
      defaultValue: 0
    },
    parentBudgetId: {
      type: DataTypes.UUID,
      references: {
        model: 'Budgets',
        key: 'id'
      }
    }
  }, {
    timestamps: true,
    paranoid: true, // Soft deletes
    indexes: [
      { fields: ['companyId'] },
      { fields: ['departmentId'] },
      { fields: ['categoryId'] },
      { fields: ['startDate', 'endDate'] }
    ]
  });

  Budget.associate = (models) => {
    Budget.belongsTo(models.Company, {
      foreignKey: 'companyId',
      as: 'company'
    });
    
    Budget.belongsTo(models.Department, {
      foreignKey: 'departmentId',
      as: 'department'
    });
    
    Budget.belongsTo(models.Category, {
      foreignKey: 'categoryId',
      as: 'category'
    });
    
    // Self-referencing association for parent budget
    Budget.belongsTo(models.Budget, {
      foreignKey: 'parentBudgetId',
      as: 'parentBudget'
    });
    
    Budget.hasMany(models.Budget, {
      foreignKey: 'parentBudgetId',
      as: 'childBudgets'
    });
  };

  return Budget;
};