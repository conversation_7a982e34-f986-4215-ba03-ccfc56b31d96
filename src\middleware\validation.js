'use strict';

const { validationResult } = require('express-validator');
const { ApiError } = require('../utils/apiError');

/**
 * Middleware to handle validation errors
 */
const validationMiddleware = (req, res, next) => {
  const errors = validationResult(req);
  
  if (!errors.isEmpty()) {
    const validationErrors = errors.array().map(error => ({
      field: error.path,
      message: error.msg
    }));
    
    return next(ApiError.validationError(validationErrors));
  }
  
  next();
};

module.exports = { validationMiddleware };