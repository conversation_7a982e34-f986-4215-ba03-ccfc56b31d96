'use strict';

const express = require('express');
const { authenticate, authorize } = require('../../../middleware/auth');
const approvalController = require('../controllers/approvalController');

const router = express.Router();

// All routes require authentication
router.use(authenticate);

/**
 * @swagger
 * /api/v1/approvals:
 *   get:
 *     summary: Get all approvals for the current user
 *     tags: [Approvals]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: status
 *         schema:
 *           type: string
 *           enum: [pending, approved, rejected, escalated]
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 20
 *     responses:
 *       200:
 *         description: List of approvals
 */
router.get('/', approvalController.getApprovals);

/**
 * @swagger
 * /api/v1/approvals/{id}:
 *   get:
 *     summary: Get approval by ID
 *     tags: [Approvals]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *     responses:
 *       200:
 *         description: Approval details
 */
router.get('/:id', approvalController.getApprovalById);

/**
 * @swagger
 * /api/v1/approvals/{id}/approve:
 *   post:
 *     summary: Approve an expense
 *     tags: [Approvals]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *     requestBody:
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               comments:
 *                 type: string
 *     responses:
 *       200:
 *         description: Expense approved successfully
 */
router.post('/:id/approve', approvalController.approveExpense);

/**
 * @swagger
 * /api/v1/approvals/{id}/reject:
 *   post:
 *     summary: Reject an expense
 *     tags: [Approvals]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - comments
 *             properties:
 *               comments:
 *                 type: string
 *     responses:
 *       200:
 *         description: Expense rejected successfully
 */
router.post('/:id/reject', approvalController.rejectExpense);

/**
 * @swagger
 * /api/v1/approvals/{id}/escalate:
 *   post:
 *     summary: Escalate an approval to higher authority
 *     tags: [Approvals]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *     requestBody:
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               reason:
 *                 type: string
 *               escalateTo:
 *                 type: string
 *                 format: uuid
 *     responses:
 *       200:
 *         description: Approval escalated successfully
 */
router.post('/:id/escalate', approvalController.escalateApproval);

/**
 * @swagger
 * /api/v1/approvals/pending:
 *   get:
 *     summary: Get all pending approvals for the current user
 *     tags: [Approvals]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: List of pending approvals
 */
router.get('/pending', approvalController.getPendingApprovals);

/**
 * @swagger
 * /api/v1/approvals/bulk-approve:
 *   post:
 *     summary: Bulk approve multiple expenses
 *     tags: [Approvals]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - approvalIds
 *             properties:
 *               approvalIds:
 *                 type: array
 *                 items:
 *                   type: string
 *                   format: uuid
 *               comments:
 *                 type: string
 *     responses:
 *       200:
 *         description: Bulk approval completed
 */
router.post('/bulk-approve', approvalController.bulkApprove);

/**
 * @swagger
 * /api/v1/approvals/bulk-reject:
 *   post:
 *     summary: Bulk reject multiple expenses
 *     tags: [Approvals]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - approvalIds
 *               - comments
 *             properties:
 *               approvalIds:
 *                 type: array
 *                 items:
 *                   type: string
 *                   format: uuid
 *               comments:
 *                 type: string
 *     responses:
 *       200:
 *         description: Bulk rejection completed
 */
router.post('/bulk-reject', approvalController.bulkReject);

/**
 * @swagger
 * /api/v1/approvals/stats:
 *   get:
 *     summary: Get approval statistics
 *     tags: [Approvals]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Approval statistics
 */
router.get('/stats', approvalController.getApprovalStats);

module.exports = router;