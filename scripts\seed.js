const { pool } = require('../config/database');
const bcrypt = require('bcryptjs');
const logger = require('../utils/logger');

async function seed() {
    try {
        logger.info('Starting database seeding...');

        // Create test user
        const hashedPassword = await bcrypt.hash('testpassword123', 12);
        
        const userResult = await pool.query(`
            INSERT INTO users (email, password_hash, subscription_tier)
            VALUES ('<EMAIL>', $1, 'premium')
            ON CONFLICT (email) DO UPDATE SET password_hash = $1
            RETURNING id
        `, [hashedPassword]);

        const userId = userResult.rows[0].id;
        logger.info(`Test user created/updated: ${userId}`);

        // Create default categories
        const categories = [
            { name: 'Food & Dining', color: '#FF6B6B', icon: 'utensils' },
            { name: 'Transportation', color: '#4ECDC4', icon: 'car' },
            { name: 'Shopping', color: '#45B7D1', icon: 'shopping-bag' },
            { name: 'Entertainment', color: '#96CEB4', icon: 'film' },
            { name: 'Bills & Utilities', color: '#FFEAA7', icon: 'file-text' },
            { name: 'Healthcare', color: '#DDA0DD', icon: 'heart' },
            { name: 'Income', color: '#98D8C8', icon: 'dollar-sign' },
            { name: 'Savings', color: '#F7DC6F', icon: 'piggy-bank' }
        ];

        for (const category of categories) {
            await pool.query(`
                INSERT INTO categories (user_id, name, color, icon, is_default)
                VALUES ($1, $2, $3, $4, true)
                ON CONFLICT DO NOTHING
            `, [userId, category.name, category.color, category.icon]);
        }

        // Create test accounts
        const accounts = [
            { name: 'Main Checking', type: 'checking', balance: 2500.00 },
            { name: 'Savings Account', type: 'savings', balance: 10000.00 },
            { name: 'Credit Card', type: 'credit', balance: -1200.00 }
        ];

        const accountIds = [];
        for (const account of accounts) {
            const result = await pool.query(`
                INSERT INTO accounts (user_id, name, type, balance)
                VALUES ($1, $2, $3, $4)
                ON CONFLICT DO NOTHING
                RETURNING id
            `, [userId, account.name, account.type, account.balance]);
            
            if (result.rows.length > 0) {
                accountIds.push(result.rows[0].id);
            }
        }

        // Get category IDs
        const categoryResult = await pool.query(
            'SELECT id, name FROM categories WHERE user_id = $1',
            [userId]
        );
        const categoryMap = {};
        categoryResult.rows.forEach(cat => {
            categoryMap[cat.name] = cat.id;
        });

        // Create sample transactions
        if (accountIds.length > 0) {
            const transactions = [
                {
                    account_id: accountIds[0],
                    category_id: categoryMap['Food & Dining'],
                    amount: 45.67,
                    type: 'expense',
                    description: 'Grocery shopping',
                    transaction_date: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000)
                },
                {
                    account_id: accountIds[0],
                    category_id: categoryMap['Transportation'],
                    amount: 25.00,
                    type: 'expense',
                    description: 'Gas station',
                    transaction_date: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000)
                },
                {
                    account_id: accountIds[0],
                    category_id: categoryMap['Income'],
                    amount: 3000.00,
                    type: 'income',
                    description: 'Salary deposit',
                    transaction_date: new Date()
                }
            ];

            for (const transaction of transactions) {
                await pool.query(`
                    INSERT INTO transactions (
                        user_id, account_id, category_id, amount, type, description, transaction_date
                    ) VALUES ($1, $2, $3, $4, $5, $6, $7)
                `, [
                    userId, transaction.account_id, transaction.category_id,
                    transaction.amount, transaction.type, transaction.description,
                    transaction.transaction_date
                ]);
            }
        }

        // Create sample budget
        if (categoryMap['Food & Dining']) {
            await pool.query(`
                INSERT INTO budgets (
                    user_id, category_id, amount, period, start_date, end_date
                ) VALUES ($1, $2, $3, 'monthly', $4, $5)
                ON CONFLICT DO NOTHING
            `, [
                userId,
                categoryMap['Food & Dining'],
                500.00,
                new Date(new Date().getFullYear(), new Date().getMonth(), 1),
                new Date(new Date().getFullYear(), new Date().getMonth() + 1, 0)
            ]);
        }

        logger.info('Database seeding completed successfully');
        process.exit(0);
    } catch (error) {
        logger.error('Seeding failed:', error);
        process.exit(1);
    }
}

seed();